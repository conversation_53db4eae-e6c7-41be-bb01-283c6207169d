<template>
	<view class="detail_all">
		<view class="nav">
			<u-navbar :is-back="true" leftIconColor="#000000" :autoBack="true" :bgColor="background" title="详情"
				:titleStyle="{ color: '#000000', fontSize: '34rpx', fontWeight: 'bold' }"></u-navbar>
		</view>
		<view v-if="!topShow" class="third-top flex align-items"
			style="padding: 30rpx;position: fixed;top: 175rpx;width: 100%;z-index: 10000;background: #fff;">
			<view class="tab-item" :class="{ 'tab-active': checkTab === 0 }" @click="switchTab(0)">活动详情
			</view>
			<view class="tab-item" :class="{ 'tab-active': checkTab === 1 }" @click="switchTab(1)"
				style="margin-left: 50rpx;">名单</view>
			<view class="tab-underline" :style="{ transform: `translateX(${checkTab * 160}rpx)` }"></view>
		</view>
		<view class="box flex justify-start flex-column align-items" :class="overlay ? 'no-scroll' : ''">
			<view class="swiper">
				<u-swiper :list="detail.images" indicator indicatorInactiveColor="#D8D8D8"
					indicatorActiveColor="#323232" indicatorMode="dot"
					:indicator-style="{ bottom: '60rpx', zIndex: 999 }" height="580rpx" circular
					@click="swiperImg"></u-swiper>
			</view>

			<view class="con-center w-100 flex justify-center flex-column align-items flex-start">
				<image src="/static/detail/conbg.png" class="topimgs"></image>
				<image src="/static/detail/rmb.png" class="topimgs_rmb"></image>

				<view class="top_texts flex align-items" v-if="detail.feel == 0">
					<span style="font-weight: 900;font-size: 56rpx;">{{ detail.price.split('.')[0] }}</span>
					<view class="flex flex-column xiao">
						<span>.{{ detail.price.split('.')[1] }}</span>
						<span>元/人</span>
					</view>
				</view>
				<view class="top_texts flex align-items" v-if="detail.feel == 1 && detail.offline==2" style="top:-140rpx;right: 32rpx;">
					<span style="font-weight: 900;font-size: 44rpx;">免费</span>
				</view>
				<view class="top_texts flex align-items" v-if="detail.offline==1" style="top:-140rpx;right: 32rpx;">
					<span style="font-weight: 900;font-size: 42rpx;">线下活动</span>
				</view>
				<view class="first-box flex flex-start flex-column justify-start">
					<view class="flex align-items">
						<span class="first-name">{{ detail.title }}</span>
					</view>



					<view class="first-image flex align-items" style="margin-top: 25rpx;width: 100%;">
						<image :src="detail.user.avatar" mode=""
							style="width: 36rpx;height: 36rpx;margin-right: 12rpx;border-radius: 80rpx;">
						</image>
						<view style="color: #9C9C9C ;width: auto;margin-right: 20rpx;">{{ detail.user.nickname }}</view>
						<view class="flex justify-center align-items xieyi">
							<image src="/static/detail/baohu.png" mode=""
								style="width: 28rpx;height: 28rpx;margin-right: 0;"></image>
							<view style="text-align: center;color: #323232;padding: 0rpx 10rpx;">{{
								detail.refund_info.title }}</view>
							<!-- <image src="/static/detail/zidong.png" mode=""
								style="width: 20rpx;height: 20rpx;margin-right: 0;"></image> -->
						</view>
					</view>

					<span class="first-image flex align-items" style="margin-top: 30rpx;">
						<image src="../../static/center/Alarm.png" mode="" class="icon-size"></image>
						<span>{{ formattedTime.formattedTime }}</span>
					</span>
					<span class="line"></span>

					<view class="first-image flex align-items">
						<image src="/static/center/address.png" mode="" class="icon-size"></image>
						<text style="font-weight: 400;width: 460rpx;padding-right: 40rpx;color: #323232;font-size: 30rpx;">{{ detail.address
						}}{{ detail.address_detail }}</text>
						<image @click.stop="toPhone()" src="/static/detail/phone.png" class="icon-size"
							style="width: 50rpx; height: 50rpx;margin-right: 35rpx;">
						</image>
						<image @click.stop="toMap(detail.latitude, detail.longitude, detail.address_detail)"
							src="/static/detail/daohang.png" class="icon-size" style="width: 50rpx; height: 50rpx;">
						</image>
					</view>
					<!-- <span class="line" style="margin-bottom: 0rpx;"></span>
					<view class="second-box flex align-items space-between" v-if="detail.join_info">
						<view class="flex align-items" v-if="detail.join_info.users">
							<u-avatar-group :urls="detail.join_info.users" keyName="avatar" size="30" gap="0.3"
								:maxCount="4"></u-avatar-group>
							<image src="/static/index/dian.png"
								:class="detail.join_info.users && detail.join_info.users.length > 0 ? '' : 'smalld'"
								style="width: 60rpx;height: 60rpx;margin-left:-20rpx;z-index: 1;"></image>
							<span class="number">{{ detail.join_info.people_number }}/{{ detail.join_info.stock >= 10000
								?
								'9999+' : detail.join_info.stock }}
								人数</span>
						</view>
						<view class="value_slide">
							<u-slider v-model="value_slide" showValue min="0" max="100" blockSize="12"
								inactiveColor="#EBEBEB" activeColor="#BBFC5B" disabled></u-slider>
						</view>
					</view> -->
				</view>
			</view>

			<!-- 报名成功 -->
			<!-- <view class="third" style="margin-bottom: 0rpx;margin-top: 30rpx;">
				<view class="third-top flex align-items" style="justify-content: space-between;padding-top: 20rpx;">
					<span>报名信息
						<image class="icons" src="/static/detail/xiangqing.png"></image>
					</span>
					<view style="margin-right: 35rpx;" @click="toSgindetail()">
						<span
							style="font-family: PingFang SC, PingFang SC;font-weight: 400;font-size: 26rpx;color: #3D3D3D;">查看全部</span>
						<image src="/static/detail/zidong.png" style="margin-left: 20rpx;width: 20rpx;height: 20rpx;">
						</image>
					</view>
				</view>
				<view v-if="detail.some_people" class="flex third-center-gro"
					style="justify-content: space-between; position: relative;height: 100%;">
					<view v-if="detail.some_people.peoples.length > 0" style="width: 100%;">
						<view v-for="(item, index) in detail.some_people.peoples" :key="index">
							<view class="flex align-items" style="justify-content: space-between;">
								<view class="">
									<view style="font-size: 28rpx;color: #323232;">{{ item.name }}</view>
									<view style="font-size: 24rpx;color: #9C9C9C;margin-top: 10rpx;">
										身份证号：{{ item.idnum }}
									</view>
								</view>
								<view class="">
									<text v-if="item.status == 3" style="font-size: 28rpx;color: #999999;">未核销</text>
									<text v-if="item.status == 6" style="font-size: 28rpx;color: #FF4810;">已核销</text>
								</view>
							</view>
							<view v-if="index < detail.some_people.peoples.length - 1"
								style="height: 1px;background-color: #F0F0F0;width: 100%;margin: 30rpx 0rpx;"></view>
						</view>
					</view>
					<view v-if="detail.some_people.peoples.length == 0" style="text-align: center;margin: 0 auto;">
						<image src="/static/detail/no_info.png" style="width: 180rpx;height: 180rpx"></image>
						<view style="font-size: 28rpx;font-weight: 300;">暂无报名信息</view>
					</view>
				</view>

			</view> -->
			<view
				style="border-radius: 44rpx;width: 100%;position: relative;margin: 20rpx 0rpx;background-color: #ffffff;">
				<view style="display: flex;justify-content: space-between;align-items: center;padding:20rpx 30rpx;">
					<view style="display: flex;align-items: center;">
						<view>
							<image src="/static/detail/wx.png" style="width: 100rpx;height: 100rpx;"></image>
						</view>
						<view style="margin-left: 20rpx;">
							<view style="font-size: 28rpx;color: #3D3D3D;">群二维码</view>
							<view style="margin-top: 10rpx;font-size: 24rpx;color: #9C9C9C;">
								上传时间：{{ formatTimestamp(detail.updatetime) }}</view>
						</view>
					</view>
					<view style="display: flex;align-items: center;">
						<view @click="openImg(detail.image)">
							<image :src="detail.image" style="width: 70rpx;height: 70rpx;"></image>
						</view>
						<u-upload @afterRead="afterRead" :maxCount="1">
							<view style="display: flex;align-items: center;">
								<view style="font-size: 28rpx;color: #323232;margin:0rpx 0rpx 0rpx 20rpx;">修改</view>
								<view>
									<image src="/static/detail/zidong.png"
										style="margin-left: 20rpx;width: 20rpx;height: 20rpx;">
									</image>
								</view>
							</view>
						</u-upload>
					</view>
				</view>
			</view>

			<view id="third" class="third flex flex-column"
				style="justify-content: flex-start;padding-top: 20rpx;padding-bottom: 30rpx;">
				<view v-if="topShow" class="third-top flex align-items" style="padding: 30rpx;">
					<view class="tab-item" :class="{ 'tab-active': checkTab === 0 }" @click="switchTab(0)">活动详情
					</view>
					<view class="tab-item" :class="{ 'tab-active': checkTab === 1 }" @click="switchTab(1)"
						style="margin-left: 50rpx;">名单</view>
					<view class="tab-underline" :style="{ transform: `translateX(${checkTab * 160}rpx)` }"></view>
				</view>
				<!-- <view class="third-top flex align-items">
					<span>活动详情
						<image class="icons" src="/static/detail/xiangqing.png"></image>
					</span>
				</view> -->
				<!-- <view class="third-center">
					<view class="v_html">
						<rich-text style="white-space: pre-line;" :nodes="detail.content"></rich-text>
					</view>
					<image mode="widthFix" class="imgs" v-for="(item, index) in detail.images" :key="index" :src="item">
					</image>
				</view> -->
				<view class="third-center1" ref="richTextContainer" v-if="!showToggleButtons" style="height: auto;">
					<view class="v_html">
						<rich-text style="white-space: pre-line;" :nodes="detail.content"></rich-text>
					</view>
				</view>
				<view class="third-center" v-if="showToggleButtons"
					:style="{ height: richTextShow ? 'auto' : '300px', overflow: 'hidden', margin: '0 auto', paddingBottom: '0' }"
					ref="richTextContainer">
					<view class="v_html">
						<rich-text style="white-space: pre-line;" :nodes="detail.content"></rich-text>
					</view>
					<!-- 仅当内容高度超过容器高度时显示展开/收起按钮 -->
					<view v-if="!richTextShow" @click="richTextShow = true"
						style="padding: 30rpx 0;position: sticky; bottom: 0;background: #fff">
						<view style=" display: flex; align-items: center; justify-content: center;margin: 0 auto;
							 background: #fff;border-radius: 200rpx;width: 288rpx;height: 80rpx;
							border: 1px solid #ff4810; color: #ff4810;font-size: 32rpx;line-height: 45rpx;">
							查看完整内容
							<!-- <u-icon name="arrow-down" color="#323232"></u-icon> -->
						</view>

					</view>
					<view v-if="richTextShow" @click="richTextShow = false"
						style="position: sticky; bottom: -16px; background: #fff; padding: 30rpx 0;">
						<view style="width: 100%; display: flex; align-items: center; justify-content: center;margin: 0 auto;
						background: #fff;border-radius: 200rpx;width: 200rpx;height: 80rpx;
						border: 1px solid #ff4810; color: #ff4810;font-size: 32rpx;line-height: 45rpx;">
							收起
							<!-- <u-icon name="arrow-up" color="#323232"></u-icon> -->
						</view>

					</view>
					<view v-if="!richTextShow"
						style="position: absolute;bottom: 140rpx;width: 100%;height: 100px;background-image: linear-gradient(-180deg, rgba(255, 255, 255, 0) 0%, rgb(255, 255, 255) 80%);">
					</view>

				</view>

				<!-- <view class="third-center" style="padding-top: 0;">
					<image mode="widthFix" class="imgs" v-for="(item, index) in detail.images" :key="index" :src="item">
					</image>
				</view> -->
				<view class=""
					style="display: flex;justify-content: flex-start;flex-wrap: wrap;width: 690rpx;margin: 0 auto;gap: 15rpx;">
					<view class="imgs" v-for="(item, index) in detail.images" :key="index">
						<image mode="aspectFill" style="width: 220rpx;height: 220rpx;border-radius: 10rpx;" :src="item"
							@click="swiperImg(index)">
						</image>
					</view>
				</view>
			</view>
			<!-- 报名信息 -->
			<view id="fourth" class="fourth flex flex-column"
				style="justify-content: flex-start;margin-bottom: 180rpx;padding-bottom: 40rpx;">
				<view class="flex align-items" style="padding: 30rpx;justify-content: space-between;">
					<view class="fourth-top flex align-items" style="margin: 0px;">
						<span>报名接龙
							<image class="icons" src="/static/detail/xiangqing.png"></image>
						</span>
					</view>

				</view>
				<view style="display: flex;justify-content: space-between;align-items: center;padding:0px 30rpx;">
					<view style="color: #9C9C9C;font-size: 28rpx;">
						<text>活动人数{{ detail.stock }}</text>
						<text style="margin-left: 30rpx;">已报{{ peopleNum }}人</text>
					</view>
					<view style="display: flex;align-items: center;" @click="openSer()">
						<view v-if="peopleParams.order == 'normal'" style="font-size: 28rpx;">默认排序</view>
						<view v-if="peopleParams.order == 'earliest'" style="font-size: 28rpx;">最早报名</view>
						<view v-if="peopleParams.order == 'new'" style="font-size: 28rpx;">最新报名</view>
						<view v-if="peopleParams.order == 'recently'" style="font-size: 28rpx;">最近报名</view>
						<view>
							<image src="https://naweigetetest2.hschool.com.cn/uniapp_image/signSort.png" mode=""
								style="width: 24rpx;height: 24rpx;margin-left: 15rpx;"></image>
						</view>
					</view>
				</view>

				<view v-for="(item, index) in signPeopleList" style="padding:30rpx 0rpx 0rpx 30rpx;">
					<view :style="{ 'paddingTop': index == 0 ? '20rpx' : '0' }">
						<view style="display: flex;align-items: center;justify-content: space-between;">
							<view>
								<view style="font-size: 32rpx;color: #3D3D3D;font-weight: 600;">
									<text>{{ (index + 1) < 10 ? '0' + (index + 1) : index + 1 }}. {{ item.name }}</text>
								</view>
								<view style="color: #9C9C9C;font-size: 30rpx;margin-top: 20rpx;padding-left: 50rpx;">
									<text>{{ formatCreateTime(item.createtime) }}</text>
									<text style="margin: 0rpx 15rpx;">·</text>
									<text>报名号{{ item.code }}</text>
									<text style="margin: 0rpx 15rpx;">·</text>
									<text v-if="item.open == 1">公开</text>
									<text v-if="item.open == 0">不公开</text>
									<view style="margin: 20rpx 0rpx;">
										<text>身份证：{{ item.idnum }}</text>
									</view>
									<view>
										<text>手机号：{{ item.mobile }}</text>
									</view>
								</view>

							</view>
						</view>
					</view>
					<view v-if="signPeopleList.length > index + 1"
						style="height: 1px;background-color: #F0F0F0;width: 93%;margin-top: 30rpx;"></view>
				</view>
			</view>

			<view class="footer" style="display: flex;align-items: center;;justify-content: space-between;gap: 30rpx;">
				<view
					v-if="(detail.status == 1 || detail.status == 2 || detail.status == 3 || detail.status == 4) && detail.auth_status == 1"
					@click="overlayShow()" style="width: 220rpx;height: 90rpx;">
					<image src="@/static/detail/fenxiangsys.png" style="width: 100%;height: 100%;" mode="widthFix">
					</image>
				</view>
				<view v-if="detail.status != -1 && detail.status != 5" class="footer-right" @click="cancelsOpen">
					取消活动
				</view>
				<view v-if="detail.status == -1" class="footer-right-no">
					活动已取消
				</view>
				<view class="footer-right-no" v-if="detail.status == 5" style="background-color: #E2E2E2;color: #999999;">活动已结束</view>
				<!-- <view class="footer flex align-items" style="justify-content: space-between;" v-if="detail.status == 5">
					
				</view> -->
			</view>





			<!-- 二次确认弹窗 -->
			<u-popup :show="cancelsShow" mode="center" :round="10" :zIndex="99999" :custom-style="popupStyle"
				@close="cancelsClose" @open="cancelsOpen" :safeAreaInsetBottom="false" :closeable="false">
				<view class="popupBox flex justify-start align-items flex-column">
					<view class="pop-header flex align-items flex-column flex-start">
						<span class="name white-space">是否确认取消活动</span>
						<span class="price">
							取消活动后，若再次举办活动，需重新设置活动内容，并提交平台审核
						</span>
						<!-- <image src="../../static/center/buy.png" mode="" style="width: 168rpx; height: 48rpx;">
					</image> -->
					</view>

					<view class="popup-footer flex ">
						<span @click="cancelsClose" class="span1">关闭</span>
						<span @click="cancels">确认取消</span>
						<!-- <image src="../../static/center/price.png" mode="" style="width: 642rpx;height: 80rpx;"></image> -->
						<!-- <u-loading-icon :vertical="true" v-if="uloadingShow"></u-loading-icon> -->
					</view>
				</view>
			</u-popup>
			<u-overlay zIndex="5555" :show="overlay"></u-overlay>
			<!-- 分享海报 -->
			<view v-if="overlay" class="pos">

				<image @click="closeoo" src="/static/center/close.png" mode=""
					style="z-index: 10000;width: 64rpx;height: 64rpx;position: absolute;top: 50rpx;right: 45rpx;">
				</image>

				<l-painter isCanvasToTempFilePath :after-delay="500" @success="sunccessimg"
					css="width:661rpx;height: 1072rpx;background-image:url(https://naweigetetest2.hschool.com.cn/dyqc/fenxiang.png);"
					custom-style="position:absolute;left:45rpx;right:44rpx;top:50rpx;z-index:100">
					<l-painter-image :src="detail.user.avatar"
						css="z-index:300;margin-left: 15rpx; margin-top: 120rpx;border: 2rpx solid #FFFFFF; width: 60rpx;  height: 60rpx; border-radius: 50%;" />
					<l-painter-view css="margin-top: 130rpx; padding-left: 20rpx; display: inline-block">
						<l-painter-text :text="detail.user.nickname + '的邀请'"
							css="display: block; height: 36rpx;color: #3D3D3D; font-size: 28rpx; fontWeight: 600;" />
					</l-painter-view>
					<l-painter-view
						css="margin-left: 20rpx; margin-top: 20rpx; margin-bottom: 20rpx; box-sizing: border-box;width: 100%;">
						<l-painter-image :src="detail.images[0]"
							css="width: 620rpx; height: 620rpx; border-radius: 24rpx;object-fit: cover;" />

						<l-painter-view
							css="margin-top: 30rpx;display: flex;justify-content: space-between;align-items: center;width:100%;">
							<l-painter-view css="display: flex;flex-direction: column;width: 420rpx;">
								<l-painter-text
									css="line-clamp: 1;font-weight: bold;color:#202020;font-size: 32rpx;width:400rpx;box-sizing:border-box;line-height: 42rpx;"
									:text="detail.title"></l-painter-text>
								<l-painter-text
									css="line-clamp: 1; color:#FF4810;font-size: 35rpx;width:220rpx;margin-top: 20rpx;line-height: 38rpx;fontWeight: 600;"
									:text="'￥' + detail.price"></l-painter-text>
								<l-painter-text
									css="line-clamp: 2; color:#9C9C9C;font-size: 26rpx;margin-top: 20rpx;line-height: 36rpx;width: 360rpx;"
									:text="'地址：' + detail.address"></l-painter-text>
							</l-painter-view>
							<l-painter-view
								css="background: #ffffff;border-radius: 18rpx;width: 200rpx; height: 200rpx;margin-right: 50rpx;">
								<l-painter-qrcode css="width: 160rpx; height: 160rpx;margin:20rpx;"
									:text="qrUrl"></l-painter-qrcode>
							</l-painter-view>

						</l-painter-view>
					</l-painter-view>
				</l-painter>

				<view class="btnList">
					<button open-type="share" class="no-border-button" plain="true">
						<image src="/static/detail/savewec.png" mode=""
							style="width: 88rpx;height: 88rpx;margin-bottom: 15rpx;"></image>
						微信
					</button>
					<!-- <view class="save"  @click.stop="sharePoster()">
						<image src="/static/detail/pyq.png" mode="" style="width: 88rpx;height: 88rpx;margin-bottom: 15rpx;"></image>
						朋友圈
					</view> -->
					<view class="save" @click="save()">
						<image src="/static/detail/donwload.png" mode=""
							style="width: 88rpx;height: 88rpx;margin-bottom: 15rpx;"></image>
						保存图片
					</view>


				</view>
				<!-- <canvas canvas-id="myCanvas"
					style="position: absolute;top:256rpx;left: 48rpx; width: 661rpx;height:1075rpx; visibility: hidden;z-index: 10;">
				</canvas> -->
			</view>


		</view>
	</view>
</template>

<script>
import dayjs from 'dayjs';
import {
	dateWeek
} from '../../utils/dateFormat'
export default {
	computed: {

		formattedTimeList() {
			return this.timeList.map(item => {
				const startTime = dayjs.unix(item.start_time).format('YYYY-MM-DD HH:mm:ss');
				const endTime = dayjs.unix(item.end_time).format('HH:mm:ss');
				return {
					formattedTime: `${startTime}~${endTime}`,
					limit_num: item.limit_num,
					sign_num: item.sign_num
				};
			});
		},
		formattedTime() {
			const startTime = dateWeek(this.detail.start_time);
			const endTime = dateWeek(this.detail.end_time);
			return {
				formattedTime: `${startTime} ~ ${endTime}`
			};
		}
	},

	data() {
		return {
			topShow: true,
			checkTab: 0,
			richTextShow: false,
			showToggleButtons: false, // 控制是否显示展开/收起按钮
			style: {
				// 字符串的形式
				img: 'width: 100%'

			},
			mysignList: [],
			join_info: {},
			value_slide: 0,
			scrollTop: 0,
			overlay: false,
			userInfo: {},
			path: 'https://testy.hschool.com.cn//uploads/20241219/3406baf51fcc28c63c31ebcee5c9c75e.jpg',
			uloadingShow: false,
			show: false,
			cancelsShow: false,
			type: 0, // 0 支付 1 立即购买 2 预约  3确认时间
			id: 1,
			count: 5,
			value: 5,
			order_no: '',
			PayPirce: 0,
			detail: {},
			people: {},
			qrUrl: '',
			is_collect: 0,
			popupStyle: {
				width: '640rpx',
				height: '414rpx',
				margin: '0 auto', // 水平居中
				display: 'flex',
				justifyContent: 'flex-start',
				alignItems: 'center',
				borderRadius: '50rpx'
			},
			timeList: [],
			selectedTime: null,
			// indexBackgroundImage: indexBackgroundImage,
			orderId: "",
			classes_lib_spec_id: '',
			order_no2: '',
			mobile: '',
			is_show_model: false, //是否显示分享模态窗
			background: '#ffffff',
			titleStyle: {
				color: '#FFFFFF'
			},
			signPeopleList: [],
			peopleParams: {
				page: 1,
				limit: 15,
				order: 'normal',
				status: '2,3,4,9'
			},
			peopleNum: 0,
		};
	},
	onShareAppMessage() {
		console.log(this.detail);
		return {
			title: this.detail.title, //分享的标题
			imageUrl: this.detail.images[0], //展示的图片，这里是本地路径的写法，也可以写http或https开头的图片路径
			path: `/packageA/center/detail?id=${this.id}`
		}
	},
	onShareTimeline() {
		return {
			title: this.detail.title, //分享的标题
			imageUrl: this.detail.headimage, //展示的图片，这里是本地路径的写法，也可以写http或https开头的图片路径
			query: `/packageA/center/detail?id=${this.id}`
		}
	},

	onLoad(options) {
		this.userInfo = uni.getStorageSync("userInfo")
		this.id = options.id
		// this.id = 1
		if (options.type == 2) {
			this.type = 2
			this.orderId = options.orderId
		}
		if (options.type == 1) {
			this.type = 1
			this.order_no = options.order_no
			this.pament()
		}
		console.log(options.id)
		this.getDetail();
		this.getPeople();
		this.getShare();
		this.getSignPeople();
	},
	mounted() {
		this.checkContentHeight();
	},
	updated() {
		this.checkContentHeight();
	},
	onReachBottom() {
		this.peopleParams.page += 1;
		this.getSignPeople();
	},
	onPageScroll(r) {
		console.log(r);
		if (r.scrollTop > 600) {
			this.topShow = false;
		} else {
			this.topShow = true;
		}
	},
	methods: {
		toPhone() {
			if (this.detail.mobile == '' || this.detail.mobile == null) {
				uni.showToast({
					title: '暂无联系电话',
					icon: 'none',
					duration: 2000
				})
				return;
			}
			//uni拨打电话
			uni.makePhoneCall({
				phoneNumber: this.detail.mobile, // 替换为你要拨打的电话号码
				success: () => {
					console.log('拨打电话成功！');
				},
				fail: () => {
					console.error('拨打电话失败！');
				}
			});
		},
		// 切换tab
		switchTab(index) {
			this.checkTab = index;

			// 滚动到对应内容区域
			// 使用 setTimeout 确保 DOM 更新完成后再获取位置
			setTimeout(() => {
				const query = uni.createSelectorQuery().in(this);
				let selector = '';

				if (index === 0) {
					// 活动详情 - 滚动到富文本内容区域
					selector = '#third';
				} else if (index === 1) {
					// 名单 - 滚动到报名信息区域
					selector = '#fourth';
				}

				if (selector) {
					query.select(selector).boundingClientRect(data => {
						if (data) {
							// 获取页面滚动信息
							uni.createSelectorQuery().selectViewport().scrollOffset(scroll => {
								// 计算目标滚动位置：元素距离文档顶部的位置 - 顶部间距
								const targetScrollTop = data.top + scroll.scrollTop - uni
									.upx2px(400);
								uni.pageScrollTo({
									scrollTop: Math.max(0, targetScrollTop),
									duration: 300 // 300ms滚动动画
								});
							}).exec();
						}
					}).exec();
				}
			}, 150); // 增加延迟确保富文本展开动画完成
		},
		openSer() {
			var that = this;
			uni.showActionSheet({
				itemList: ['默认排序', '最早报名', '最新报名', '最近报名'],
				success: function (res) {
					if (res.tapIndex == 0) {
						that.peopleParams.order = 'normal';
					} else if (res.tapIndex == 1) {
						that.peopleParams.order = 'earliest';
					} else if (res.tapIndex == 2) {
						that.peopleParams.order = 'new';
					} else {
						that.peopleParams.order = 'recently';
					}
					that.signPeopleList = [];
					that.peopleParams.page = 1;
					that.getSignPeople();
				},
				fail: function (res) {
					console.log(res.errMsg);
				}
			});
		},
		//报名人信息列表
		getSignPeople() {
			uni.$u.http.get('/api/school.new_activity/people_list', {
				params: {
					activity_id: this.id,
					page: this.peopleParams.page,
					limit: this.peopleParams.limit,
					order: this.peopleParams.order,
					status: this.peopleParams.status,
					my: 1,
				}
			}).then(res => {
				if (res.code == 1) {
					console.log('peopleList', res.code, res.data);
					this.signPeopleList.push(...res.data.list);
					this.peopleNum = res.data.count;
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none',
						duration: 2000
					})
				}
			}).catch(error => { });
		},
		checkContentHeight() {
			// 使用 uni.createSelectorQuery 获取富文本容器的高度
			const query = uni.createSelectorQuery().in(this);
			query.select('.v_html').boundingClientRect(data => {
				if (data && data.height > 300) { // 300px 是容器的固定高度
					this.showToggleButtons = true;
				} else {
					this.showToggleButtons = false;
				}
			}).exec();
		},
		swiperImg(index) {
			uni.previewImage({
				current: index,
				urls: this.detail.images,
			});
		},
		async afterRead(item) {
			const result = await this.uploadFilePromise(item.file.url, 'user');
			console.log(result);
			if (result.code != 1) {
				uni.showToast({
					title: result.msg,
					icon: 'none',
					duration: 2000
				});
				return;
			} else {
				uni.$u.http.post('/api/school.new_activity/edit_qrcode', {
					id: this.id,
					image: result.data.url,
				}).then(res => {
					if (res.code == 1) {
						uni.showToast({
							title: '上传成功！',
							icon: 'none',
							duration: 2000
						});
						this.getDetail();
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {
					uni.showToast({
						title: '请求失败，请稍后再试',
						icon: 'none',
						duration: 2000
					});
				});
			}
		},
		uploadFilePromise(url, category) {
			console.log('category', category)
			return new Promise((resolve, reject) => {
				let a = uni.uploadFile({
					url: 'https://naweigetetest2.hschool.com.cn/api/common/upload', // 仅为示例，非真实的接口地址
					filePath: url,
					name: 'file',
					formData: {
						user: 'test',
						category: category
					},
					header: {
						"token": uni.getStorageSync("token")
					},
					success: (res) => {
						console.log(res);
						var d = JSON.parse(res.data);
						resolve(d);
					},
					fail: (err) => {
						reject(err);
					}
				});
			});
		},
		openImg(img) {
			uni.previewImage({
				urls: [img],
			});
		},
		formatTimestamp(timestamp) {
			const date = new Date(timestamp * 1000); // 10位时间戳需要乘以1000
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, "0");
			const day = String(date.getDate()).padStart(2, "0");
			const hours = String(date.getHours()).padStart(2, "0");
			const minutes = String(date.getMinutes()).padStart(2, "0");
			const seconds = String(date.getSeconds()).padStart(2, "0");
			return `${year}.${month}.${day}`;
		},
		sunccessimg(event) {
			this.path = event
		},
		closeoo() {
			this.overlay = false;
		},
		getShare() {
			uni.$u.http.post('/api/wechat_util/link', {
				path: 'packageA/center/detail',
				query: `id=${this.id}`,
			}).then(res => {
				if (res.code == 1) {
					this.qrUrl = res.data.url_link
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none',
						duration: 2000
					});
				}
			}).catch(error => {
				uni.showToast({
					title: '请求失败，请稍后再试',
					icon: 'none',
					duration: 2000
				});
			});
		},
		overlayShow() {
			const token = uni.getStorageSync('token')
			if (token) {
				let that = this;
				uni.showToast({
					title: '海报生成中...',
					icon: 'none',
					duration: 2000,
					complete: function () {
						// 提示框消失后的回调函数
						setTimeout(() => {
							// 这里写你的后续操作代码
							that.overlay = true;
						}, 2000);
					}
				});

			} else {
				uni.showToast({
					title: '请登录',
					icon: 'none',
					duration: 2000,
					complete: function () {
						setTimeout(function () {
							uni.switchTab({
								url: '/pages/my/index',
							});
						}, 2000);
					}
				});
			}
		},
		// 格式化创建时间为 MM-DD HH:mm 格式
		formatCreateTime(timestamp) {
			const date = new Date(timestamp * 1000); // 10位时间戳需要乘以1000
			const month = String(date.getMonth() + 1).padStart(2, "0");
			const day = String(date.getDate()).padStart(2, "0");
			const hours = String(date.getHours()).padStart(2, "0");
			const minutes = String(date.getMinutes()).padStart(2, "0");
			return `${month}-${day} ${hours}:${minutes}`;
		},
		// 时间转换函数
		timeago(timestamp) {
			const now = new Date().getTime(); // 当前时间（毫秒）
			const diff = (now - timestamp * 1000) / 1000; // 时间差（秒）

			if (diff < 60) {
				return `${Math.floor(diff)}秒前`;
			} else if (diff < 3600) {
				return `${Math.floor(diff / 60)}分钟前`;
			} else if (diff < 86400) {
				return `${Math.floor(diff / 3600)}小时前`;
			} else if (diff < 2592000) { // 30天
				return `${Math.floor(diff / 86400)}天前`;
			} else {
				return `${Math.floor(diff / 2592000)}个月前`;
			}
		},

		// 获取课时规格
		getTime() {
			uni.$u.http.get('/api/school/classes/spec', {
				params: {
					id: this.id,
				}
			}).then(res => {
				if (res.code == 1) {

					this.timeList = res.data.spec;
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none',
						duration: 2000
					});
				}
			}).catch(error => {
				uni.showToast({
					title: '请求失败，请稍后再试',
					icon: 'none',
					duration: 2000
				});
			});
		},
		toSgindetail() {
			uni.navigateTo({
				url: '/packageA/center/signDetail?id=' + this.id
			});
		},
		// 获取报名详情
		getPeople() {
			const that = this
			uni.$u.http.get('/api/school.newworker.activity.order/order_list', {
				params: {
					activity_id: that.id,
					page: 1,
					limit: 1000,
				},
			}).then(res => {
				if (res.code == 1) {
					this.mysignList = res.data.list;
				} else {
					that.showErrorToast(res.msg);
				}
			}).catch(error => {
				that.showErrorToast('请求失败，请稍后再试');

			});
		},
		// 提示
		showErrorToast(msg) {
			uni.showToast({
				title: msg,
				icon: 'none',
				duration: 2000
			});
		},
		//取消活动
		cancels() {
			uni.$u.http.post('/api/school.new_activity/cancel', {
				id: this.id,
			}).then(res => {
				if (res.code == 1) {
					uni.showToast({
						title: '取消成功',
						icon: 'success',
						duration: 2000
					})
					setTimeout(() => {
						uni.redirectTo({
							url: "/packageA/my/orderList"
						})
					}, 1000)
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none',
						duration: 2000
					})
				}
			}).catch(error => { });
		},
		// 获取详情
		getDetail() {
			uni.$u.http.get('/api/school.new_activity/detail', {
				params: {
					id: this.id,
				}
			}).then(res => {
				if (res.code == 1) {
					this.detail = res.data.detail;
					this.join_info = res.data.detail.join_info;
					this.value_slide = res.data.detail.join_info.percent;
					if (res.data.detail.is_collect != 0) {
						this.is_collect = 1
					} else {
						this.is_collect = 0
					}
					if (this.detail.user.realname) {
						this.detail.user.realname = this.detail.user.realname.slice(0, 1) + 'XX';
					}
					this.mobile = this.detail.user.mobile.slice(0, 3) + 'XXXX' + this.detail.user.mobile.slice(
						7);
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none',
						duration: 2000
					})
				}
			}).catch(error => { });
		},

		sign() {

			// this.cancelsShow = true
			// this.type = 3;

			uni.navigateTo({
				url: '/packageA/center/applyDetail?id=' + this.id
			})

		},
		buy() {
			this.uloadingShow = true
			this.getMoney()
		},
		// 取消弹窗 
		cancelsOpen() {
			this.cancelsShow = true
		},
		// 预约弹窗 type = 2 0 支付 1 立即购买 2 预约  3确认时间
		open() {
			this.show = true
			this.type = 3
		},
		// 确认时间  type = 0  0 支付 1 立即购买 2 预约  3确认时间
		// confimTime() {
		// 	uni.$u.http.post('/api/school/hour_order/confirm', {
		// 		classes_order_id: this.orderId,
		// 		classes_lib_spec_id: this.classes_lib_spec_id,
		// 		order_no: this.order_no2,
		// 		is_compute: 1
		// 	}).then(res => {
		// 		if (res.code == 1) {
		// 			this.order_no2 = res.data.order_no
		// 			this.timeCreat(res.data.order_no)
		// 		} else {
		// 			uni.showToast({
		// 				title: res.msg,
		// 				icon: 'none',
		// 				duration: 2000
		// 			})
		// 			this.type = 2
		// 			// _this.$api.toast(res.msg);
		// 		}
		// 	}).catch(error => {

		// 	});

		// 	this.show = false
		// 	this.type = 0
		// },
		// // 预约下单
		// timeCreat(order_no) {
		// 	uni.$u.http.post('/api/school/hour_order/create', {
		// 		order_no: order_no,
		// 	}).then(res => {
		// 		if (res.code == 1) {
		// 			uni.showToast({
		// 				title: res.msg,
		// 				icon: 'none',
		// 				duration: 2000,
		// 				complete: function() {
		// 					setTimeout(function() {
		// 						uni.reLaunch({
		// 							url: "/packageA/my/makeList?status=" + -1
		// 						})
		// 					}, 2000);
		// 				}

		// 			})

		// 		} else {
		// 			uni.showToast({
		// 				title: res.msg,
		// 				icon: 'none',
		// 				duration: 2000
		// 			})
		// 			this.type = 2
		// 			// _this.$api.toast(res.msg);
		// 		}
		// 	}).catch(error => {

		// 	});
		// },
		// 导航
		toMap(latitude, longitude, name) {
			uni.openLocation({
				latitude: parseFloat(latitude),
				longitude: parseFloat(longitude),
				name: name,
				success: function () {
					console.log('success');
				}
			});
		},


		close() {
			this.type = 0
			this.selectedTime = null
			this.show = false
		},
		cancelsClose() {
			this.cancelsShow = false
		},
		// 返回首页
		toIndex() {
			uni.switchTab({
				url: "/pages/index/index"
			})
		},
		// 保存海报
		save() {
			let base64 = this.path.replace(/^data:image\/\w+;base64,/, ""); //图片替换
			let filePath = wx.env.USER_DATA_PATH + '/qrcode.png';
			uni.getFileSystemManager().writeFile({
				filePath: filePath, //创建一个临时文件名
				data: base64, //写入的文本或二进制数据
				encoding: 'base64', //写入当前文件的字符编码
				success: (res) => {
					uni.saveImageToPhotosAlbum({
						filePath: filePath,
						success: () => {
							uni.showToast({
								title: '保存成功',
								icon: "none",
								duration: 5000
							})
						},
						fail: (err) => {
							console.log(err);
							uni.showToast({
								title: '保存失败',
								icon: "none",
								duration: 5000
							})
						}
					})
				},
				fail: (err) => {
					console.log(err)
				}
			})
		},
		//分享发布
		sharePoster() { //分享图片给好友按钮的点击事件函数
			let that = this
			this.base64ToFilePath(this.path, (filePath) => {
				console.log(filePath);
				wx.showShareImageMenu({ //分享给朋友
					path: filePath,
					success: (res) => {
						console.log("分享成功：", res);
					},
					fail: (err) => {
						console.log("分享取消：", err);
					},
				})
			})
		},


		// 收藏和取消
		Collect(number) {
			uni.$u.http.post('/api/school/classes/collect', {
				id: this.id,
				is_collect: number
			}).then(res => {
				if (res.code == 1) {
					this.is_collect = number
					if (number == 0) {
						uni.showToast({
							title: '取消收藏',
							icon: 'none',
							duration: 2000
						})
					} else {
						uni.showToast({
							title: '收藏成功',
							icon: 'none',
							duration: 2000
						})
					}

				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none',
						duration: 2000
					})
					// _this.$api.toast(res.msg);
				}
			}).catch(error => {

			});
		},
		callPhone(phone) {
			uni.makePhoneCall({
				phoneNumber: phone
			})
		},

		// 取消选择
		cancel() {
			this.selectedTime = null
		},
		selectTime(time) {
			this.selectedTime = time;
			this.classes_lib_spec_id = time.id

		},
		timeSelected(time) {
			return this.selectedTime === time;
		},
		moveScroll() { },
		// 获取价格
		getMoney() {
			uni.$u.http.post('/api/school.newactivity.order/confirm', {
				activity_id: this.id,
				order_no: this.order_no,
				is_compute: 1,
				num: 1
			}).then(res => {
				if (res.code == 1) {
					this.PayPirce = res.data.order_data.totalprice
					this.order_no = res.data.order_no
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none',
						duration: 2000
					})
					// _this.$api.toast(res.msg);
				}
				this.uloadingShow = false
			}).catch(error => {

			});
		},
	},
}
</script>

<style lang="scss" scoped>
.detail_all {
	background-color: #f7f7f7;
}

.w-100 {
	width: 100%;
}

.flex {
	display: flex;
}

.flex-start {
	align-items: flex-start;
}

.justify-center {
	justify-content: center;
}

.align-items {
	align-items: center;
}

.flex-column {
	flex-flow: column;
}

.justify-start {
	justify-content: start;
}

.white-space {
	overflow: hidden;
	/* 确保超出容器的文本被隐藏 */
	white-space: nowrap;
	/* 确保文本在一行内显示 */
	text-overflow: ellipsis;
	/* 使用省略号表示被截断的文本 */
	width: 100%;
}

.con-center {
	background: white;
	border-radius: 44rpx;
	position: relative;
	height: 100%;
}



.space-between {
	justify-content: space-between;
}

.swiper {
	width: 100%;
	height: 580rpx;
}

.box {
	padding-top: 175rpx;
	position: relative;
}

.topimgs_rmb {
	position: absolute;
	top: -168rpx;
	right: 30rpx;
	width: 201rpx;
	height: 118rpx;
	z-index: 0;
}

.topimgs {
	position: absolute;
	top: -120rpx;
	width: 100%;
	z-index: 0;
	height: 100%;
}

.top_texts {
	position: absolute;
	top: -148rpx;
	right: 30rpx;
	z-index: 3;
	color: #ffffff;
	width: 200rpx;
	text-align: center;
	display: flex;
	justify-content: center;

	.xiao {
		margin-left: 4rpx;
		font-size: 22rpx;
		font-weight: 400;
	}
}

.first-box {
	width: 690rpx;
	background: #FFFFFF;
	// background: url('@/static/detail/conbg.png');
	// padding-left: 24rpx;
	// margin-top: 20rpx;
	border-radius: 20rpx;
	z-index: 1;
	padding-bottom: 20rpx;

	.sigh {
		width: 88rpx;
		height: 40rpx;
		background: #BEEE03;
		border-radius: 4rpx 4rpx 4rpx 4rpx;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 500;
		font-size: 24rpx;
		color: #222222;
	}

	.first-name {
		width: 690rpx;
		height: 52rpx auto;
		font-family: PingFang SC, PingFang SC;
		font-weight: 600;
		font-size: 36rpx;
		color: #323232;
		// // margin-left: 16rpx;
		// overflow: hidden;
		// /* 确保超出容器的文本被隐藏 */
		// white-space: nowrap;
		// /* 确保文本在一行内显示 */
		// text-overflow: ellipsis;
		// /* 使用省略号表示被截断的文本 */
	}


	.first-mine {
		height: 32rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 24rpx;
		color: #7A7A7A;

	}

	.first-txt {
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 24rpx;
		color: #7A7A7A;
		line-height: 32rpx;
		margin: 0 6rpx;
	}

	.first-image {
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 24rpx;
		color: #7A7A7A;

		span {
			width: 600rpx;
			// height: 40rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 30rpx;
			color: #222222;
		}

		.xieyi {
			background-color: #BBFC5B;
			height: 48rpx;
			border-radius: 8rpx;
			padding: 0px 10rpx;
		}
	}
}

.second-box {
	width: 690rpx;
	height: 64rpx;
	border-radius: 16rpx 16rpx 16rpx 16rpx;
	margin: 20rpx 0 20rpx 0;

	.smalld {
		margin-left: 0 !important;
	}

	.number {
		height: 40rpx;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 400;
		font-size: 26rpx;
		color: #323232;
		margin-left: 32rpx;
	}

	view {
		span {
			background: rgba(255, 255, 255, 0.4);
			border-radius: 24rpx;
		}
	}

	// .value_slide{
	// 	width: 50%;
	// 	::v-deep .uni-slider-handle-wrapper{
	// 		height: 10rpx;
	// 	}
	// 	::v-deep .uni-slider-handle{
	// 		background: url('@/static/detail/qiu.png') !important;
	// 		border-radius: 0;
	// 		background-size: 52rpx 52rpx !important;
	// 	}
	// 	::v-deep .uni-slider-value{
	// 		color: #323232;
	// 		&::after{
	// 			content: '%';
	// 		}
	// 	}
	// }

}

.third-top {
	font-family: PingFang SC, PingFang SC;
	font-weight: bold;
	font-size: 36rpx;
	color: #323232;
	line-height: 50rpx;
	position: relative;
	z-index: 10;
}

.third {
	width: 100%;
	background: #ffffff;
	border-radius: 44rpx;

	c span {
		position: relative;

		.icons {
			width: 37rpx;
			height: 20rpx;
			position: absolute;
			left: 0;
			bottom: 0;
			z-index: -1;
		}
	}

	.third-center {
		padding: 30rpx;
		overflow: hidden;
		margin: 0 auto;
		position: relative;

		.v_html {
			font-size: 34rpx;
			line-height: 44rpx;
		}

		.imgs {
			width: 690rpx;
			display: block;
		}
	}

	.third-center1 {
		padding: 30rpx;
		overflow: hidden;

		.v_html {
			font-size: 34rpx;
			line-height: 44rpx;
		}

		.imgs {
			width: 690rpx;
			display: block;
		}
	}

	.third-center-gro {
		margin-top: 28rpx;
		width: auto;
		height: 160rpx;
		overflow: hidden;
		position: relative;
		margin-left: 30rpx;
		margin-right: 30rpx;
		margin-bottom: 30rpx;

		.scroll-container {
			.t1_name {
				width: 100rpx;
				height: 36rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 26rpx;
				color: #3D3D3D;
				line-height: 36rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}

			.t1_order {
				width: 350rpx;
				height: 36rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 26rpx;
				color: #3D3D3D;
				line-height: 36rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}

			.t1_num {
				width: 103rpx;
				height: 36rpx;

				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 26rpx;
				color: #3D3D3D;
				line-height: 36rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}
		}
	}



}

.line {
	width: 690rpx;
	height: 2rpx;
	background: #F0F0F0;
	margin: 19rpx 0;
}

.icon-size {
	width: 32rpx;
	height: 32rpx;
	margin-right: 12rpx;
}


.footer {
	width: 93%;
	padding: 0 30rpx;
	height: 185rpx;
	background: #ffffff;
	border-radius: 0rpx 0rpx 0rpx 0rpx;
	position: fixed;
	z-index: 99;
	/* 绝对定位 */
	bottom: 0;
	/* 定位在底部 */
	left: 0;
	right: 0;
	margin: 0 auto;
	/* 定位在左边 */

	.footer-left {
		position: absolute;
		left: 100rpx;
		display: flex;

		view {
			display: flex;
			justify-content: center;
			align-items: center;
			flex-flow: column;
			margin-right: 40rpx;
			width: 48rpx;

			span {
				text-align: center;
				width: 48rpx;
				height: 34rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 22rpx;
				color: #A4A4A4;
			}
		}

		image {
			width: 191rpx;
			height: 118rpx;
		}
	}

	.bottom_fot {
		width: 100%;
		height: 147rpx;
		background: #FFFFFF;
		position: fixed;
		bottom: 0;
	}

	.footer-right {
		margin: 0 auto;
		width: 100%;
		height: 90rpx;
		background: #323232;
		text-align: center;
		line-height: 90rpx;
		border-radius: 200rpx;
		font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
		font-weight: 400;
		font-size: 48rpx;
		color: #BBFC5B;
	}
}


.popupBox {
	width: 640rpx;
	height: 414rpx;
	background-image: url("https://naweigetetest2.hschool.com.cn/dyqc/confirm2.png");
	background-size: 100%;
	background-repeat: no-repeat;
	border-radius: 44rpx;

	.pop-header {
		width: 100%;

		background-repeat: no-repeat;
		background-position: left bottom;
		height: 414rpx;
		margin-top: 80rpx;

		span {
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 20rpx;
			color: #343434;
		}

		.name {
			width: 288rpx;
			height: 36rpx;
			font-family: PingFang SC Bold, PingFang SC Bold;
			font-weight: 900;
			font-size: 36rpx;
			color: #202020;
			line-height: 36rpx;
			text-align: center;
			font-style: normal;
			text-transform: none;
		}

		.price {
			width: 520rpx;
			height: 68rpx;
			margin-top: 30rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 24rpx;
			color: #202020;
			line-height: 34rpx;
			text-align: center;
			font-style: normal;
			text-transform: none;
		}
	}

	.popup {
		display: flex;
		align-items: self-start;
		justify-content: center;
		width: 594rpx;
	}

	.popup-footer {
		position: absolute;
		left: 75rpx;
		bottom: 60rpx;

		span {
			width: 230rpx;
			height: 90rpx;
			background: #323232;
			border-radius: 200rpx 200rpx 200rpx 200rpx;
			font-family: PingFang SC Regular, PingFang SC Regular;
			font-weight: 400;
			font-size: 32rpx;
			color: #BBFC5B;
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.span1 {
			background: rgba(193, 193, 193, 0.22);
			color: #202020;
			margin-right: 30rpx;
		}

	}


	.line {
		width: 642rpx;
		height: 1rpx;
		background: #F0F0F0;
		box-shadow: 1rpx 1rpx 0rpx 0rpx rgba(102, 102, 102, 0.25);
		border-radius: 0rpx 0rpx 0rpx 0rpx;
	}



	.selectTime.selected {
		width: 288rpx;
		height: 50rpx;
		border-radius: 12rpx 12rpx 12rpx 12rpx;
		background: #008CFF;
		font-family: PingFang SC, PingFang SC;
		font-weight: 800;
		font-size: 24rpx;
		color: #FFFFFF;
		cursor: pointer;
		margin: 24rpx 32rpx 0 0;

	}
}

.share {
	position: fixed;
	color: #FFFFFF;
	right: 0;
	bottom: 190rpx;
	background: linear-gradient(to bottom right, #FE726B, #FE956B);
	padding: 10rpx 10rpx 10rpx 20rpx;
	border-top-left-radius: 50px;
	border-bottom-left-radius: 50px;
	box-shadow: 0 0 20upx rgba(0, 0, 0, .09);
}

.cancel {
	width: 100vw;
	padding: 30rpx;
	text-align: center;
	background: #FFFFFF;
	color: red;
	font-weight: bold;
	font-size: 30rpx;
}

.md-content {
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	padding: 50rpx 0;
	background-color: white;
}

.md-content-item {
	margin: 0 70rpx;
	position: relative;
}

.md-content-item image {
	width: 100rpx;
	height: 100rpx;
}

.md-content-item view {
	margin-top: 15rpx;
	font-size: 28rpx;
}

.sharebtn {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	opacity: 0;
}

.cu-modal {
	position: fixed;
	bottom: 166rpx;
	left: 0;
	z-index: 999999;
}

.gj {
	.title {
		font-family: PingFang SC, PingFang SC;
		font-weight: 800;
		font-size: 32rpx;
		color: #4B4B4B;

		span {
			font-family: PingFang SC, PingFang SC;
			font-weight: 800;
			font-size: 24rpx;
			color: #4B4B4B;
		}
	}

	.scroll {
		width: 642rpx;
		max-height: 340rpx;

		view {
			margin: 24rpx;
			width: 600rpx;
			height: 56rpx;
			background: #E8E8E8;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			display: flex;
			justify-content: center;
			align-items: center;

			.title {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 28rpx;
				color: #7A7A7A;
			}

			.red {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 28rpx;
				color: #FF5F5F;
			}

			.lan {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 28rpx;
				color: #008CFF;
			}
		}
	}
}

::v-deep ::-webkit-scrollbar {
	/*滚动条整体样式*/
	width: 4px !important;
	height: 1px !important;
	overflow: auto !important;
	background: #ccc !important;
	-webkit-appearance: auto !important;
	display: block;
}

::v-deep ::-webkit-scrollbar-thumb {
	/*滚动条里面小方块*/
	border-radius: 10px !important;
	box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2) !important;
	background: #7b7979 !important;
}

::v-deep ::-webkit-scrollbar-track {
	/*滚动条里面轨道*/
	// box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2) !important;
	// border-radius: 10px !important;
	background: #FFFFFF !important;
}

.Poster {
	position: relative;
	top: 21rpx;
	left: 30rpx;
	width: 690rpx;
}

.posterClose {
	position: absolute;
	right: 8rpx;
	top: 8rpx;
}

.no-scroll {
	overflow: hidden;
	height: 100vh;
}

::v-deep ._root {
	padding: 0 10rpx;
}
</style>
<style lang="scss">
.value_slide {
	width: 40%;

	::v-deep .uni-slider-handle-wrapper {
		height: 10rpx;
	}

	::v-deep .uni-slider-handle {
		background: url('@/static/detail/qiu.png') !important;
		border-radius: 0;
		background-size: 36rpx 36rpx !important;
		width: 36rpx;
		height: 36rpx;
		top: 14rpx;
		margin-left: -18rpx !important;
	}

	::v-deep .uni-slider-value {
		color: #323232;

		&::after {
			content: '%';
		}
	}

	// #ifdef MP-WEIXIN
	.wx-slider-handle-wrapper {
		height: 8rpx;
	}

	.wx-slider-handle {
		background: url('@/static/detail/qiu.png') !important;
		border-radius: 0;
		background-size: 30rpx 30rpx !important;
		width: 28rpx;
		height: 28rpx;
		top: 14rpx;
		margin-left: -14rpx !important;
	}

	.wx-slider-value {
		display: flex;
		width: 30rpx;
		color: #323232;

		&::after {
			content: '%';
		}
	}

	// #endif
}

.pos {
	position: fixed;
	width: 100%;
	height: 100%;
	z-index: 99999;

	.posterClose {
		position: absolute;
		// right: 8rpx; 
		top: 200rpx;
	}
}

.btnList {
	position: absolute;
	bottom: 173rpx;
	display: flex;
	justify-content: center;
	align-items: self-start;
	width: 750rpx;
	height: 247rpx;
	background: #FFFFFF;
	border-radius: 44rpx 44rpx 0rpx 0rpx;
	color: #999999;
	font-family: PingFang SC Regular, PingFang SC Regular;
	font-weight: 400;
	font-size: 28rpx;
	gap: 180rpx;

	.save {
		margin-top: 60rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}
}

.footer-right-no {
	margin: 0 auto;
	width: 100%;
	height: 90rpx;
	background: #E1E1E1;
	text-align: center;
	line-height: 90rpx;
	border-radius: 200rpx;
	font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
	font-weight: 400;
	font-size: 48rpx;
	color: #999999;
}

.fourth {
	width: 100%;
	background: #ffffff;
	margin-top: 20rpx;
	border-radius: 44rpx;

	.fourth-top {
		font-family: PingFang SC, PingFang SC;
		font-weight: bold;
		font-size: 36rpx;
		color: #323232;
		line-height: 50rpx;
		margin-top: 30rpx;
		margin-left: 30rpx;
		position: relative;
		z-index: 10;
	}

	span {
		position: relative;

		.icons {
			width: 37rpx;
			height: 20rpx;
			position: absolute;
			left: 0;
			bottom: 0;
			z-index: -1;
		}
	}

	.mgbot {
		margin-bottom: 210rpx;
	}

}

.imgs {
	display: block;
	border-radius: 10rpx;
	width: 220rpx;
	height: 220rpx;
}
</style>
<style scoped>
.no-border-button {
	background-color: transparent;
	/* 去掉背景色 */
	border: none;
	/* 去掉边框 */
	padding: 0;
	/* 去掉内边距 */
	margin: 0;
	/* 去掉外边距 */
	display: inline-flex;
	/* 使按钮内容居中 */
	align-items: center;
	/* 垂直居中 */
	justify-content: center;
	/* 水平居中 */
	flex-flow: column;
	line-height: inherit;
	margin-top: 60rpx;
	font-family: PingFang SC Regular, PingFang SC Regular;
	font-weight: 400;
	font-size: 28rpx;
	color: #999999;
}

.tab-underline {
	position: absolute;
	bottom: 15rpx;
	left: 70rpx;
	height: 6rpx;
	background-color: #323232;
	width: 50rpx;
	border-radius: 3rpx;
	transition: transform 0.3s ease;
}

.tab-item {
	cursor: pointer;
	transition: color 0.3s ease;
	color: #9C9C9C;
	position: relative;
}

.tab-active {
	color: #323232 !important;
}
</style>
<style>
.rich_class {
	word-wrap: break-all !important;
	word-break: pre-line !important;
}
</style>