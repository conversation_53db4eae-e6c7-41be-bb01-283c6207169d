<template>
	<!-- <view>编辑活动</view> -->
	<view class="allbg">
		<view class="backImg flex flex-column align-items">
			<!-- logo -->
			<!-- <view class="title_logo">
				<image src="/static/fabu/fabuhd.png" style="width: 237rpx; height: 57rpx"></image>
			</view> -->

			<scroll-view @touchmove.prevent scroll-y="true" :show-scrollbar="true"
				class="scroll-container box flex flex-column align-items">
				<view class="first flex flex-column align-items justify-start">
					<view class="row flex align-items" style="margin-top: 25rpx;">
						<span>
							<input type="text" maxlength="30" placeholder="填写清晰的活动标题" class="inputl"
								v-model="form.title" placeholder-class="bttop" />
						</span>
						<span style="font-size: 24rpx;color: #9C9C9C;">(必填30字内)</span>
					</view>


					<view class="row flex align-items textarea_fb" style="margin-top: 25rpx;">
						<u--textarea :confirmType="null" v-model="form.content" :maxlength="800" type="text"
							placeholder="描述一下活动的亮点、活动内容、推荐的人群、叫大家一起运动吧~" :height="120"
							placeholder-class="bttop"></u--textarea>
					</view>

					<span class="line-row" style="margin-top: 0;"></span>

					<!-- 图片 -->
					<view class="shenfen row flex flex-column" style="margin-top: 42rpx;">
						<u-upload :fileList="fileList1" @afterRead="afterRead" :previewFullImage="true"
							@delete="deletePic" @oversize="oversize" name="1" multiple :maxSize="1 * 1024 * 1024"
							:maxCount="9">
							<view class="pic_view">
								<image src="@/static/index/upload1.png" mode="widthFix"
									style="width: 44rpx;height: 44rpx;" />
								<span class="texts">添加图片</span>
								<!-- <span>{{fileList1[0].url}}</span> -->
							</view>
						</u-upload>
					</view>

					<span class="line-row"></span>
					<view class="row flex align-items" style="margin-top: 25rpx; ">
						<view class="label flex align-items" style="width: auto;">
							<image src="@/static/fabu/jhdd.png" mode="widthFix"
								style="width: 44rpx;height: 44rpx;padding-right: 8rpx;" />
							集合地点
						</view>
						<view class="row-right" @click="choose()" style="width: 70%;">
							<text
								:style="form.address == '' ? 'font-size: 28rpx;color: #9C9C9C;' : 'font-size: 28rpx;color: #3D3D3D;'">{{
									form.address == '' ? '请选择地址' : form.address }}</text>
							<u-icon name="arrow-right" color="#323232"></u-icon>
						</view>
					</view>

					<view class="row flex align-items textarea_mph" style="margin-top: 30rpx;">
						<u--textarea v-model="form.address_detail" maxlength="400" placeholder-class="bttops"
							placeholder="请输入详细地址/楼、门牌号"></u--textarea>
					</view>

					<view class="row flex align-items" style="margin: 30rpx 0;">
					
						<view class="label flex align-items" style="width: auto;">
							<image src="@/static/fabu/hdsj.png" mode="widthFix"
								style="width: 44rpx;height: 44rpx;padding-right: 8rpx;" />
							活动开始时间
						</view>
						<view class="row-right" @click="dateShowHidden()" style="width: 60%;">
							<!-- <input type="text" placeholder="请选择活动时间" class="input" disabled="true" v-model="form.time"
													placeholder-class="plasty" style="width: auto;"/> -->
							<text
								:style="form.start_time == '' ? 'font-size: 28rpx;color: #3D77FF;' : 'font-size: 28rpx;color: #3D3D3D;'">{{
														form.start_time == '' ? '报名结束即开始' : form.start_time }}</text>
							<u-icon name="arrow-right" color="#323232"></u-icon>
						</view>
					</view>
					
					
					
					<span class="line-row" style="margin-top: 0;"></span>
					<view class="row flex align-items" style="margin: 30rpx 0;">
					
						<view class="label flex align-items" style="width: auto;">
							<image src="@/static/fabu/hdsj.png" mode="widthFix"
								style="width: 44rpx;height: 44rpx;padding-right: 8rpx;" />
							活动结束时间
						</view>
						<view class="row-right" @click="actEndDateOpen()" style="width: 60%;">
							<!-- <input type="text" placeholder="请选择活动时间" class="input" disabled="true" v-model="form.time"
													placeholder-class="plasty" style="width: auto;"/> -->
							<text
								:style="form.end_time == '' ? 'font-size: 28rpx;color: #9C9C9C;' : 'font-size: 28rpx;color: #3D3D3D;'">{{
														form.end_time == '' ? '请选择活动结束时间' : form.end_time }}</text>
							<u-icon name="arrow-right" color="#323232"></u-icon>
						</view>
					</view>
				</view>
				<view class="first flex flex-column align-items justify-start"
					style="margin-top: 20rpx;padding-bottom: 25rpx;">
					<view class="row flex align-items" style="margin-top: 25rpx;">
						<view class="label flex align-items ">
							客服电话
						</view>
						<view class="row-right">
							<input type="text" placeholder="请填写客服电话" class="input" v-model="form.mobile"
								placeholder-class="plasty" />
						</view>
					</view>
				</view>
				<view class="first flex flex-column align-items justify-start" style="margin-top: 20rpx;">
					<view class="row flex align-items" style="margin-top: 25rpx;">
						<span class="label flex align-items " style="width: 190rpx;">
							<span style="color: #3D3D3D;">*</span>
							报名开始时间
						</span>
						<span class="row-right" @click="birthShowHidden()">
							<text
								:style="form.sign_start_time == '' ? 'font-size: 28rpx;color: #3D77FF;' : 'font-size: 28rpx;color: #3D3D3D;'">{{
														form.sign_start_time == '' ? '保存即开始' : form.sign_start_time }}</text>
							<!-- <input type="text" placeholder="请选择报名时间" class="input" disabled="true"
													v-model="form.sign_time" placeholder-class="plasty" /> -->
							<u-icon name="arrow-right" color="#323232"></u-icon>
						</span>
					</view>
					<span class="line-row"></span>
					<view class="row flex align-items" style="margin-top: 25rpx;">
						<span class="label flex align-items " style="width: 190rpx;">
							<span style="color: #3D3D3D;">*</span>
							报名结束时间
						</span>
						<span class="row-right" @click="signUpEndShowOpen()">
							<text
								:style="form.sign_end_time == '' ? 'font-size: 28rpx;color: #9C9C9C;' : 'font-size: 28rpx;color: #3D3D3D;'">{{
														form.sign_end_time == '' ? '请选择报名结束时间' : form.sign_end_time }}</text>
							<!-- <input type="text" placeholder="请选择报名时间" class="input" disabled="true"
													v-model="form.sign_time" placeholder-class="plasty" /> -->
							<u-icon name="arrow-right" color="#323232"></u-icon>
						</span>
					</view>
					<span class="line-row" style="margin-top: 0px;"></span>
					<view class="row flex align-items" style="margin-top: 25rpx;">
						<span class="label flex align-items ">
							<span style="color: #3D3D3D;">*</span>活动分类
						</span>
						<span class="row-right" @click="openBq">
							<!-- <input type="text" placeholder="请选择活动分类" class="input" disabled="true"
								v-model="form.cate_idsName" placeholder-class="plasty" /> -->
							<text
								:style="form.cate_idsName == '' ? 'font-size: 28rpx;color: #9C9C9C;' : 'font-size: 28rpx;color: #3D3D3D;'">{{
									form.cate_idsName == '' ? '请选择活动分类' : form.cate_idsName }}</text>
							<u-icon name="arrow-right" color="#323232"></u-icon>
						</span>
					</view>
					<span class="line-row"></span>


					<view class="row flex align-items" style="margin: 25rpx 0;">
						<view class="label flex align-items ">
							<text style="color: #3D3D3D;">*</text>退款政策
						</view>
						<!--  -->
						<view class="row-right" @click="tuikuan = true">
							<!-- <input type="text" placeholder="请选择退款政策" class="input" disabled="true"
								v-model="form.refund_idn" placeholder-class="plasty_c" /> -->
							<text style="font-size: 28rpx;color: #FF4810;">{{ form.refund_idn == '' ? '请选择退款政策' :
								form.refund_idn }}</text>
							<u-icon name="arrow-right" color="#323232"></u-icon>
						</view>
					</view>

				</view>

				<view class="first flex flex-column align-items justify-start" style="margin-top: 20rpx;width: 100%;">
					<view class="row flex align-items" style="margin-top: 25rpx;">
						<view style="width: 40%;" class="label flex align-items ">
							<text style="color: #3D3D3D;">*</text>群二维码上传
						</view>
						<view class="row-right">
							<u-upload @afterRead="uploadQun" :maxCount="1">
								<view style="display: flex;justify-content: flex-end;align-items: center;">
									<view v-if="qunQrcode == ''" style="color: #9C9C9C;font-size: 28rpx;">未上传</view>
									<!-- <view v-if="qunQrcode != ''" style="color: #FF4810;font-size: 28rpx;">已上传</view> -->
									<view v-if="qunQrcode != ''" style="color: #FF4810;font-size: 28rpx;">
										<!-- <image :src="qunQrcode.url" style="width: 70rpx;height: 70rpx;"></image> -->
										<image :src="QunQrcode1.url" style="width: 70rpx;height: 70rpx;"></image>

									</view>
									<u-icon name="arrow-right" color="#323232"></u-icon>
								</view>
							</u-upload>
						</view>
					</view>
					<span class="line-row" style="background-color: #ffffff;"></span>
				</view>

				<view class="first flex flex-column align-items justify-start" style="margin-top: 20rpx;">
					<view class="row flex align-items" style="margin-top: 25rpx;">
						<span class="label flex align-items ">
							<span style="color: #3D3D3D;">*</span>
							活动人数
						</span>
						<span class="row-right">
							<input type="number" placeholder="请填写活动人数" placeholder-class="plasty" class="input"
								v-model="form.stock" />
						</span>
					</view>
					<span class="line-row"></span>
					
					<view class="row flex align-items" style="margin-top: 25rpx;">
						<span class="label flex align-items ">
							<span style="color: #3D3D3D;">*</span>
							付费方式
						</span>
						<view class="row-right" @click="openPayTab()">
							<view style="display: flex;justify-content: flex-end;align-items: center;">
								<view style="color: #323232;font-size: 28rpx;">
									<text style="color: #9C9C9C;" v-if="payTab == -1">请选择付费方式</text>
									<text v-if="payTab == 0">线上免费活动</text>
									<text v-if="payTab == 1">线上付费活动</text>
									<text v-if="payTab == 2">线下活动</text>
								</view>
								<u-icon name="arrow-right" color="#323232"></u-icon>
							</view>
						</view>
					</view>
					<span v-if="payTab == 1" class="line-row"></span>
					<view v-if="payTab == 1" class="row flex align-items" style="margin-top: 25rpx;">
						<view class="label flex align-items ">
							<span style="color: #3D3D3D;">*</span>
							活动价格
						</view>
						<view class="row-right" style="font-size:28rpx;color:#FF4810;font-weight: 600;"
							@click="priceShow = true">
							<!-- <input type="digit" placeholder="请填写活动价格" class="input" v-model="priceName"
								placeholder-class="plasty" /> -->
							<view>
								<view v-if="form.price == 0 || form.price == ''">
									<text>{{ priceName }}</text>
								</view>
								<view v-if="form.price != '' && form.price != 0">
									<text>￥</text>
									<text>{{ priceName }}</text>
									<text>/人</text>
								</view>
							</view>
							<view>
								<u-icon name="arrow-right" color="#323232"></u-icon>
							</view>
						</view>
					</view>
					<span class="line-row" style="background-color: #ffffff;"></span>

				</view>
			</scroll-view>
			<view style="height: 500rpx;"></view>
			<view class="bottom " v-if="status == -1 || status == 2">
				<view style="margin:30rpx 0 0 0;">
					<cc-protocolBox :agree="agree" :name="protocolArr" @click="protocolClick"
						@clickOne="protocolClick"></cc-protocolBox>
				</view>
				<view class="btns">
					<view class="saveDraft" @click="editDraft()">存草稿</view>
					<!-- <view class="draftBox">草稿箱</view> -->
					<view class="submitPublish" @click="apply()" v-if="agreeAdd == true">确认发布</view>
					<!-- <view class="saveDraft" @click="editDraft()" v-if="agreeAdd == false" style="background: #EEEEEE;color: #9C9C9C;">存草稿</view> -->
					<!-- <view class="draftBox">草稿箱</view> -->
					<view class="submitPublish" @click="apply()" v-if="agreeAdd == false"
						style="background: #EEEEEE;color: #9C9C9C;">确认发布</view>
				</view>
				<!-- <span class="flex align-items justify-center" @click="apply()" v-if="agreeAdd == true">确认发布</span>
				<span class="flex align-items justify-center" v-if="agreeAdd == false"
					style="background: #EEEEEE;color: #9C9C9C;">确认发布</span> -->
			</view>

			<!-- 退款政策 -->
			<u-popup @touchmove.native.stop.prevent :closeable="false" :show="tuikuan" :round="22" mode="bottom"
				@close="closetuikuan" @open="opentuikuan" :custom-style="popupStyletk">
				<view class="popup_tkall">
					<view style="display: flex;justify-content: space-between;align-items: center;">
						<view class="closetk" @click="closetuikuan">取消</view>
						<view class="popup_tk"> 选择退款政策</view>
						<view class="confirmtk" @click="closetuikuan">确认</view>
					</view>
					<scroll-view scroll-y="true" class="popup-content">
						<view class="popup-content-item flex align-items" :class="[{ active: currents === index }]"
							v-for="(item, index) in refund_list" :key="index" @click="selectItem(index, item)">
							<view style="width: 540rpx;">
								<span class="popup-content-item-title">{{ item.title }}</span>
								<span v-html="item.desc" class="popup-content-item-content"
									style="margin-top: 20rpx;"></span>
							</view>
							<image v-if="currents === index" src="@/static/fabu/check.png" mode="widthFix"
								style="width: 44rpx;height: 44rpx;" />
							<image v-else src="@/static/fabu/nocheck.png" mode="widthFix"
								style="width: 44rpx;height: 44rpx;" />
						</view>

					</scroll-view>
				</view>
			</u-popup>

			<!-- 声明 -->
			<u-popup @touchmove.native.stop.prevent :closeable="false" :show="show" :round="10" mode="center"
				@close="close" @open="open" :custom-style="popupStyle" :safeAreaInsetBottom="false"
				:closeOnClickOverlay="false">
				<span style="font-size: 40rpx;font-weight: 800;height: 120rpx;">《责任承诺确认书》</span>
				<scroll-view ref="scrollView" :scroll-top="scrollTop" :show-scrollbar='true'
					@scrolltolower="handleScroll" scroll-y="true" style="height: 800rpx;">
					<view class="popup flex align-items flex-column">
						<rich-text style="text-align: justify;" :nodes="Negotiate"></rich-text>
					</view>
				</scroll-view>
				<!-- <view class="popup-footer">
					<span class="zhixiao" v-if="agreeShow == false">我同意</span>
					<span class="zhixiao shows_zhidao" v-if="agreeShow == true" @click="change">我同意</span>
				</view> -->
				<view style="gap: 20rpx;width: 100%;display: flex;justify-content: space-between;align-items: center;">
					<view class="btn_4" @click="show = false">取消</view>
					<view class="btn_3" v-if="agreeShow == false">我同意</view>
					<view class="btn_2" v-if="agreeShow == true" @click="change()">我同意</view>
				</view>
			</u-popup>

			<!-- 身份证是否认证 -->
			<u-popup @touchmove.native.stop.prevent :closeable="false" :show="cardShow" :round="10" mode="center"
				@close="cardShow = false" :safeAreaInsetBottom="false" :custom-style="{
					width: '600rpx',
					padding: '24rpx 24rpx 20rpx 24rpx',
					margin: '0 auto',
					display: 'flex',
					justifyContent: 'start',
					alignItems: 'center',
					flexColumn: 'column'
				}" :closeOnClickOverlay="false">
				<view style="text-align: center;margin-top: 30rpx;">
					<image src="/static/fabu/renzheng.png" style="width: 140rpx;height: 140rpx;"></image>
				</view>
				<view style="font-size: 36rpx;color: #3D3D3D;margin-top: 30rpx;font-weight: 600;">完成认证，即可发布活动</view>
				<view
					style="padding: 40rpx 20rpx 10rpx 20rpx;;font-weight: 400;color: #3D3D3D;text-align: center;line-height: 44rpx;font-size: 28rpx;">
					只差最后一步！完成身份认证，即可成为<br>【搭+】认证活动官，开始分享您的精彩活动。
				</view>
				<view
					style="gap: 20px;width: 100%;display: flex;justify-content: space-between;align-items: center;padding-bottom: 30rpx;">
					<view class="btn_4" @click="cardShow = false">取消</view>
					<view class="btn_2" @click="openUrl('/packageB/card/index')">我同意</view>
				</view>
			</u-popup>


			<!-- 活动分类 -->
			<u-popup :show="showPopbq" mode="bottom" round="20"
				:customStyle="{ 'width': '750rpx', 'height': '1040rpx', 'zIndex': '999' }" :closeable="false"
				@close="closebq" :closeOnClickOverlay="false">
				<image @click="closebq" src="@/static/center/close.png" mode=""
					style="width: 44rpx;height: 44rpx;position: absolute;right: 30rpx;top: -160rpx;z-index: 155;">
				</image>
				<view class="popup_bq">
					<!-- <view @click="closebq" style="z-index: 200;width: 100%;text-align: right;padding-right: 40rpx;padding-top: 20rpx;">确认</view> -->
					<img src="https://naweigetetest2.hschool.com.cn/dyqc/hdfenlei.png" alt="" />
					<view class="flex flex-column w-100 bqlist">
						<scroll-view scroll-y="true" class="flex align-items allbqs">
							<view class="titles_fl" v-if="list.length > 0">已选择</view>
							<view class="flex align-items allmybqs">
								<view class="flex align-items bqpiece" v-for="(item_bq, index) in list" :key="index"
									@click="removebq(index)">
									<span>
										{{ item_bq.name }}
									</span>
									<u-icon name="close" color="#babdc7"></u-icon>
								</view>
							</view>

							<view class="titles_fl">全部标签</view>

							<view style="display: flex; flex-wrap: wrap;">
								<view class="flex align-items" :class="['bqpiece', { active: current === index }]"
									v-for="(item, index) in bqList" :key="index" @click="addbq(item, index)">
									<span>
										{{ item.name }}
									</span>
								</view>
							</view>
						</scroll-view>
						<view>
							<view @click="closebq" class="btn_1">确定选择</view>
						</view>
					</view>

				</view>
			</u-popup>

			<!-- 活动开始时间 @change="handleDatePickerChange"-->
			<u-popup :show="dateShow" mode="bottom" round="20" @open="dateShowHidden()"
				:customStyle="{ 'width': '750rpx', 'height': '1040rpx', 'zIndex': '999' }" :closeable="false"
				@close="dateShow = false" :closeOnClickOverlay="false">
				<view style="display: flex;justify-content: space-between;align-items: center;padding: 30rpx;">
					<view style="font-size: 28rpx;color: #9C9C9C;" @click="dateShowCancel">取消</view>
					<view style="font-size: 36rpx;color: #3D3D3D;font-weight: 600;">活动开始时间</view>
					<view style="font-size: 28rpx;color: #3D3D3D;" @click="sureStartTime()">确认</view>
			
				</view>
				<view style="height: 1px;background-color: #EEEEEE;width: 100%;"></view>
				<view style="height: 700rpx;">
					<long-date v-if="dateShow" :default-time="defaultTime" chooseNum="90"
						@select="datefirmStartTime($event)"></long-date>
				</view>
			</u-popup>
			
			<!-- 活动结束时间 @change="handleDateEndPickerChange"-->
			<u-popup :show="actEndShow" mode="bottom" round="20" @open="actEndDateOpen()"
				:customStyle="{ 'width': '750rpx', 'height': '1040rpx', 'zIndex': '999' }" :closeable="false"
				@close="actEndShow = false" :closeOnClickOverlay="false">
				<view style="display: flex;justify-content: space-between;align-items: center;padding: 30rpx;">
					<view style="font-size: 28rpx;color: #9C9C9C;" @click="endTimeShowCancel">取消</view>
					<view style="font-size: 36rpx;color: #3D3D3D;font-weight: 600;">活动结束时间</view>
					<view style="font-size: 28rpx;color: #3D3D3D;" @click="sureEndTime()">确认</view>
				</view>
				<view style="height: 1px;background-color: #EEEEEE;width: 100%;"></view>
				<view style="height: 700rpx;">
					<long-date v-if="actEndShow" :default-time="defaultTime" chooseNum="90"
						@select="datefirmStartTimeEnd($event)"></long-date>
				</view>
			</u-popup>
			
			<!-- 报名开始时间 -->
			<u-popup :show="birthShow" mode="bottom" round="20"
				:customStyle="{ 'width': '750rpx', 'height': '1040rpx', 'zIndex': '999' }" :closeable="false"
				@close="birthShow = false" :closeOnClickOverlay="false">
				<view style="display: flex;justify-content: space-between;align-items: center;padding: 30rpx;">
					<view style="font-size: 28rpx;color: #9C9C9C;" @click="birthShowCancel">取消</view>
					<view style="font-size: 36rpx;color: #3D3D3D;font-weight: 600;">报名开始时间</view>
					<view style="font-size: 28rpx;color: #3D3D3D;" @click="sureBirthShow()">确认</view>
				</view>
				<view style="height: 1px;background-color: #EEEEEE;width: 100%;"></view>
				<view style="height: 700rpx;">
					<long-date v-if="birthShow" :default-time="defaultTime" chooseNum="90"
						@select="birthSignStartConfirm($event)"></long-date>
				</view>
			</u-popup>
			
			<!-- 报名结束时间 -->
			<u-popup :show="signUpEndShow" mode="bottom" round="20"
				:customStyle="{ 'width': '750rpx', 'height': '1040rpx', 'zIndex': '999' }" :closeable="false"
				@close="signUpEndShow = false" :closeOnClickOverlay="false">
				<view style="display: flex;justify-content: space-between;align-items: center;padding: 30rpx;">
					<view style="font-size: 28rpx;color: #9C9C9C;" @click="signUpEndShowCancel">取消</view>
					<view style="font-size: 36rpx;color: #3D3D3D;font-weight: 600;">报名结束时间</view>
					<view style="font-size: 28rpx;color: #3D3D3D;" @click="signUpSure()">确认</view>
				</view>
				<view style="height: 1px;background-color: #EEEEEE;width: 100%;"></view>
				<view style="height: 700rpx;">
					<long-date v-if="signUpEndShow" :default-time="defaultTime" chooseNum="90"
						@select="birthSignEndConfirm($event)"></long-date>
				</view>
			</u-popup>
			
			
			<!-- 价格 -->
			<u-popup :show="priceShow" :round="20" :closeable="false" mode="bottom" @close="priceShow = false;">
				<view style="display: flex;justify-content: space-between;align-items: center;padding: 0px 40rpx;">
					<view style="color: #9C9C9C;font-size: 28rpx;" @click="priceShow = false">取消</view>
					<view
						style="padding: 26rpx;font-size: 36rpx;font-weight: 400;color: #3D3D3D;text-align: center;font-weight: 600;">
						活动价格
					</view>
					<view style="color: #3D3D3D;font-size: 28rpx;" @click="priceDo">确定</view>
				</view>
				<view style="padding: 0rpx 30rpx;">
					<view style="padding: 30rpx 0rpx;border: 2px solid #EEEEEE;border-radius: 20rpx;">
						<view>
							<input :focus="priceShow" :maxlength="5" :cursor-spacing="300" type="digit"
								placeholder="请输入" v-model="price" style="text-align: center;" />
						</view>
					</view>
					<view style="font-size: 24rpx;color: #FF4810;margin-top: 20rpx;">
						注意：【活动金额为0时，将不支持自动退款。若金额设置为1或以上，可设置其他退款政策。】</view>
				</view>
				<!-- <view style="margin-top: 40rpx;">
					<view class="btn_1" @click="priceDo" style="margin: 0 auto;">确认</view>
				</view> -->
			</u-popup>
			<!-- 活动时间 -->
			<!-- <u-datetime-picker @cancel="datecel" ref="dateRef" title="开始时间" :minDate="minDate" confirmText="下一步"
				@confirm="datefirm" :show="dateShow" v-model="form.date" mode="datetime"></u-datetime-picker> -->
			<!-- <u-datetime-picker @cancel="datecel1" ref="dateRef" title="结束时间" :minDate="minDate1" @confirm="datefirm1"
				:show="dateShow1" v-model="form.date1" mode="datetime"></u-datetime-picker> -->


			<!-- 报名时间 -->
			<!-- <u-datetime-picker @cancel="birthCancel" ref="birthRef" title="开始时间" :minDate="minDate"
				@confirm="birthConfirm" :show="birthShow" v-model="form.birth" mode="datetime"></u-datetime-picker>
			<u-datetime-picker @cancel="birthCancel1" ref="birthRef" title="结束时间" :minDate="minDate1"
				@confirm="birthConfirm1" :show="birthShow1" v-model="form.birth1" mode="datetime"></u-datetime-picker> -->

			<!-- 保存草稿箱弹框 -->
			<u-popup :show="baoDraftShow" mode="center" :round="10" :zIndex="99999" :custom-style="{
				width: '600rpx',
				padding: '24rpx 24rpx 20rpx 24rpx',
				margin: '0 auto',
				display: 'flex',
				justifyContent: 'start',
				alignItems: 'center',
				flexColumn: 'column'
			}" @close="baoDraftCancel" @open="baoDraftShow = true" :safeAreaInsetBottom="false" :closeable="false">
				<view style="text-align: center;margin-top: 30rpx;">
					<image src="/static/fabu/renzheng.png" style="width: 140rpx;height: 140rpx;"></image>
				</view>
				<view style="font-size: 36rpx;color: #3D3D3D;margin-top: 30rpx;font-weight: 600;">草稿保存成功</view>
				<view
					style="padding: 40rpx 20rpx 10rpx 20rpx;;font-weight: 400;color: #3D3D3D;text-align: center;line-height: 44rpx;font-size: 28rpx;">
					已存至「我的-我发布的」中
				</view>
				<view
					style="gap: 20px;width: 100%;display: flex;justify-content: center;align-items: center;padding-bottom: 30rpx;">
					<view class="btn_2" @click="baoDraftCancel">知道了</view>
				</view>
			</u-popup>


		</view>
	</view>
</template>

<script>
import dayjs from 'dayjs';
import longDate from "@/components/long-date/long-date.vue"
import {
	dateWeek,
	dateWeekData
} from '../utils/dateFormat'
export default {
	components: {
		longDate
	},
	data() {
		return {
			price: '',
			priceName: '免费',
			priceShow: false,
			bmIndex: 1,
			hdIndex: 1,
			agreeAdd: false,
			timer: null,
			timeLog: 0,
			showPopbq: false, //标签弹窗
			bqList: [], //标签列表
			cate_id: '', //标签id
			list: [],
			refund_list: [], //退款list
			peopleShow: false, //活动弹窗
			inputValue: '', // 用于绑定输入框的值
			inputType: 'text', // 输入框类型
			
			birthShow1: false, // 报名日期弹窗
			value: false,
			scrollTop: 0,
			minDate: dayjs().startOf('day').valueOf(),
			minDate1: dayjs().startOf('day').valueOf(),
			old: {
				scrollTop: 0
			},
			status: -1,
			reason: '',
			dateShow: false, //活动开始时间
			actEndShow: false, //活动结束显示
			birthShow: false, // 报名开始显示
			signUpEndShow:false,//报名结束显示
			sign_start_time: '', //报名开始时间
			sign_end_time: '', //报名结束时间
			start_time: '', //活动开始时间
			end_time: '', //活动结束时间
			dateShow1: false,
			show: false,
			tuikuan: false,
			State: '',
			Negotiate: null, // 入驻协议
			agree: false,
			agreeShow: false,
			protocolArr: "《活动发布者责任与承诺确认书》",
			payTab: -1,
			form: {
				feel: '',
				offline: '',
				mobile:'',
				cate_ids: '',
				// 活动分类名字
				cate_idsName: "",
				content: '',
				refund_id: '',
				refund_idn: '',
				price: '',
				stock: '',
				sign_time: '',
				time: '',
				sign_start_time: '', //报名开始时间
				sign_end_time: '', //报名结束时间
				start_time: '', //活动开始时间
				end_time: '', //活动结束时间
				images: '',
				title: '',
				address: '',
				latitude: '',
				longitude: '',
				address_detail: '', //详细位置
				date: dayjs().add(4, 'hour').valueOf(), //活动开始时间
				date1: dayjs().add(8, 'hour').valueOf(), //活动结束时间
				birth: dayjs().add(4, 'hour').valueOf(), //报名开始日期
				birth1: dayjs().add(8, 'hour').valueOf(), //报名结束日期
			},
			apply_info: {},
			popupStyle: {
				width: '620rpx',
				padding: '50rpx 40rpx 42rpx 40rpx',
				height: '984rpx',
				margin: '0 auto', // 水平居中
				display: 'flex',
				justifyContent: 'start',
				alignItems: 'center',
				flexColumn: 'column'
			},
			popupStyletk: {
				width: '710rpx',
				padding: '24rpx 24rpx 42rpx 24rpx',
				height: '984rpx',
				margin: '0 auto', // 水平居中
				display: 'flex',
				justifyContent: 'start',
				alignItems: 'center',
				flexColumn: 'column'
			},
			popupStyletkDraft: {
				width: '100%',
				padding: '24rpx 0rpx 24rpx 0rpx',
				height: '984rpx',
				margin: '0 auto', // 水平居中
				display: 'flex',
				justifyContent: 'start',
				alignItems: 'center',
				flexColumn: 'column',
				backgroundColor: '#f7f7f7',
			},
			current: -1,
			currents: -1,
			institutionList: [],
			images: '',
			fileList1: [], // 用于存储第一个上传组件的文件列表
			list1: '', // 存储第一个上传组件的图片URL，最多3张
			//活动转换的时间
			times_b: '', //开始时间
			times_e: '', //结束时间

			times_b_int: '', //开始时间
			times_e_int: '', //结束时间

			//报名转换的时间
			times_sinb: '', //开始时间
			times_sine: '', //结束时间

			times_sinb_int: '', //开始时间
			times_sine_int: '', //结束时间

			qunQrcode: '',
			QunQrcode1: '', //仅用于页面展示
			boxHeight: 0,
			cardShow: false,
			cardStatus: 0,
			id: null,
			original_activity_id: null,
			type: null,
			tempDefaultTime: null,
			isFormValid: false, //草稿箱的控制
			draftList: [],
			draftShow: false,
			keywords: '',
			loadStatus: "loading",
			selectHeadIndex: null,
			draftSelectIndex: null,
			selectDradtForm: {
				cate_ids: '',
				// 活动分类名字
				cate_idsName: "",
				content: '',
				refund_id: '',
				refund_idn: '',
				price: '',
				stock: '',
				sign_time: '',
				time: '',
				images: '',
				title: '',
				address: '',
				latitude: '',
				longitude: '',
				address_detail: '', //详细位置
				date: dayjs().add(4, 'hour').valueOf(), //活动开始时间
				date1: dayjs().add(8, 'hour').valueOf(), //活动结束时间
				birth: dayjs().add(4, 'hour').valueOf(), //报名开始日期
				birth1: dayjs().add(8, 'hour').valueOf(), //报名结束日期
			},
			draftId: null,
			baoDraftShow: false,
		};
	},
	onLoad(options) {
		console.log('options', options.draftId);
		this.type = options.type
		this.draftId = options.draftId
		console.log('this.type', this.type);
		if (this.draftId) {
			console.log('this.draftId', this.draftId);
			this.agree = false
			this.getDraftDetail()
		}
		uni.hideShareMenu();
		this.getAgreement()
		this.getBqList();
		this.getrefund_list();
		this.getitembq();
		this.selectItemTuikuan();
		//var c=uni.getSystemInfoSync();
		//844-94-70-100 = 580
		//763-47-(100-47)-34-50
		//this.computeBoxHeight();

	},
	onShow() {
		this.show = false;
		this.getCardInfo();
	},
	filters: {
		formatTimestamp(value) {
			if (!value) return '';
			// const date = new Date(value * 1000); // 将秒转换为毫秒
			const date = new Date(new Date().getTime() + 8 * 60 * 60 * 1000)
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1，并补零
			const day = String(date.getDate()).padStart(2, '0'); // 补零
			return `${year}-${month}-${day}`;
		}
	},
	methods: {
		openPayTab() {
			var that = this;
			uni.showActionSheet({
				itemList: ['线上免费活动', '线上付费活动', '线下活动'],
				success: function (res) {
					that.payTab = res.tapIndex;
					if (res.tapIndex == 0) {
						that.form.feel = 1;
						that.form.offline = 2;
					} else if (res.tapIndex == 1) {
						that.form.feel = 0;
						that.form.offline = 2;
					} else {
						that.form.feel = 0;
						that.form.offline = 1;
					}
				},
				fail: function (res) {
					console.log(res.errMsg);
				}
			});
		},
		// 获取详情
		getDetail() {
			// uni.switchTab({
			// 	url:
			// })//跳转tabbar页面的
			uni.$u.http.get('/api/school.new_activity/detail', {
				params: {
					id: this.id || this.original_activity_id,
				}
			}).then(res => {
				if (res.code == 1) {
					this.form = res.data.detail
					if (res.data.detail.feel == 1 && res.data.detail.offline == 2) {
						this.payTab = 0;
					} else if (res.data.detail.feel == 0 && res.data.detail.offline == 2) {
						this.payTab = 1;
					} else {
						this.payTab = 2;
					}
					this.price = this.form.price
					this.priceDo()
					this.form.content = this.form.content.replace(/<[^>]+>/g, ''); // 输出: "运动"
					this.value_slide = res.data.detail.join_info.percent;
					const idBqSet = new Set(this.form.cate_ids);
					const bqArray = this.bqList.reduce((acc, obj) => {
						if (this.form.cate_ids.includes(obj.id)) {
							acc.push(obj);
						}
						return acc;
					}, []);
					console.log('bqArray', bqArray);
					let arrIdsName = bqArray.map((item) => {
						return item.name
					})
					this.form.cate_idsName = arrIdsName.join(',');
					console.log('cate_idsName', this.form.cate_idsName);
					this.list = bqArray
					this.form.cate_ids = res.data.detail.cate_ids;
					this.form.mobile=res.data.detail.mobile;
					console.log('cate_ids', this.form.cate_ids);
					this.form.refund_idn = this.form.refund_info.title;
					// 转换为 fileList 格式
					this.fileList1 = this.form.images.map(url => ({
						url: url,
						status: 'success',
						message: ''
					}));
					this.list1 = this.form.images
					console.log('fileList1', this.fileList1);

					this.qunQrcode = ({
						url: this.form.image,
						status: 'success',
						message: ''
					})
					this.QunQrcode1 = this.qunQrcode
					console.log('qunQrcode', this.qunQrcode);

					//活动时间
					this.start_time = this.form.start_time;
					this.end_time = this.form.end_time;
					this.form.start_time = this.form.start_time_text.slice(5, 16);
					this.form.end_time = this.form.end_time_text.slice(5, 16);
					// const time_start = this.form.start_time_text.slice(5, 16)
					// const time_end = this.form.end_time_text.slice(5, 16)
					// this.form.time = time_start + ' - ' + time_end
					//报名时间
					this.sign_start_time = this.form.sign_start_time;
					this.sign_end_time = this.form.sign_end_time;
					this.form.sign_start_time = this.form.sign_start_time_text.slice(5, 16);
					this.form.sign_end_time = this.form.sign_end_time_text.slice(5, 16);
					// const signTime_start = this.form.sign_start_time_text.slice(5, 16)
					// const signTime_end = this.form.sign_end_time_text.slice(5, 16)
					// this.form.sign_time = signTime_start + ' - ' + signTime_end;
					
					
					
					if (!this.original_activity_id) {
						this.times_b_int = this.form.start_time_text
						this.times_e_int = this.form.end_time_text
						this.times_sinb_int = this.form.sign_start_time_text
						this.times_sine_int = this.form.sign_end_time_text
					} else {
						this.times_b_int = ''
						this.times_e_int = ''
						this.times_sinb_int = ''
						this.times_sine_int = ''
						this.form.sign_time = ''
						this.form.time = ''
					}
					if (this.type == 2) {
						this.form.title = ''
					}

					this.mobile = this.detail.user.mobile.slice(0, 3) + 'XXXX' + this.detail.user.mobile.slice(
						7);
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none',
						duration: 2000
					})
				}
			}).catch(error => { });
		},
		//草稿箱详情
		getDraftDetail() {
			console.log('getDraftDetail', this.draftId);
			uni.$u.http.get('/api/school.newactivity.activity_drafts/detail', {
				params: {
					id: this.draftId,
				}
			}).then(res => {
				if (res.code == 1) {
					this.form = res.data.detail;
					if (res.data.detail.feel == 1 && res.data.detail.offline == 2) {
						this.payTab = 0;
					} else if (res.data.detail.feel == 0 && res.data.detail.offline == 2) {
						this.payTab = 1;
					} else {
						this.payTab = 2;
					}
					console.log('selectSureDraft', this.form.cate_ids);
					console.log('cate_ids', this.form.cate_ids);
					this.form.cate_idsName = this.form.classes_cate.join(',');
					const idBqSet = new Set(this.form.cate_ids);
					const bqArray = this.bqList.reduce((acc, obj) => {
						if (this.form.cate_ids.includes(obj.id)) {
							acc.push(obj);
						}
						return acc;
					}, []);
					console.log('bqArray', bqArray);
					this.list = bqArray
					// this.form.cate_ids = res.data.detail.cate_ids;
					console.log('cate_ids', this.list);


					// if (res.data.detail.start_time_text != '') {
					// 	console.log('活动时间接参');
					// 	const time_start = this.form.start_time_text.slice(5, 16)
					// 	const time_end = this.form.end_time_text.slice(5, 16)
					// 	this.form.time = time_start + ' - ' + time_end
					// 	this.times_b_int = this.form.start_time_text;
					// 	this.times_e_int = this.form.end_time_text

					// }

					// if (res.data.detail.sign_start_time_text != '' && res.data.detail.sign_end_time_text != '') {

					// 	const signTime_start = this.form.sign_start_time_text.slice(5, 16)
					// 	const signTime_end = this.form.sign_end_time_text.slice(5, 16)
					// 	this.form.sign_time = signTime_start + ' - ' + signTime_end

					// 	this.times_sinb_int = this.form.sign_start_time_text;
					// 	this.times_sine_int = this.form.sign_end_time_text;
					// 	console.log('报名时间接参', this.times_sinb_int, this.times_sine_int);
					// }
					//活动时间
					if(this.form.start_time != '' && this.form.start_time_text != '') {
						this.start_time =  this.form.start_time_text.slice(0, 16);
						this.form.start_time = this.form.start_time_text.slice(5, 16);
					}else {
						this.form.start_time = ''
					}
					if(this.form.end_time != '' && this.form.end_time_text != '') {
						this.end_time = this.form.end_time;
						this.form.end_time = this.form.end_time_text.slice(5, 16);
					}else {
						this.form.end_time = ''
					}
					//报名时间
					if(this.form.sign_start_time != '' && this.form.sign_start_time_text != '') {
						this.sign_start_time = this.form.sign_start_time;
						this.form.sign_start_time = this.form.sign_start_time_text.slice(5, 16);
					}else {
						this.form.sign_start_time = ''
					}
					if(this.form.sign_end_time != '' && this.form.sign_end_time_text != '') {
						this.sign_end_time = this.form.sign_end_time;
						this.form.sign_end_time = this.form.sign_end_time_text.slice(5, 16);
					}else {
						this.form.sign_end_time = ''
					}

					if (this.form.refund_info != null) {
						this.form.refund_idn = this.form.refund_info.title;
						console.log('refund_idn', this.form.refund_idn);
					}

					// 转换为 fileList 格式
					if (this.form.images != '') {
						this.fileList1 = this.form.images.map(url => ({
							url: url,
							status: 'success',
							message: ''
						}));
						this.list1 = this.form.images
						console.log('fileList1', this.fileList1);
					}
					if (this.form.image != '' && this.form.image != null) {
						this.qunQrcode = ({
							url: this.form.image,
							status: 'success',
							message: ''
						})
						this.QunQrcode1 = this.qunQrcode
						console.log('qunQrcode', this.qunQrcode);
					}



					this.price = this.form.price
					this.priceDo()

					if (!this.original_activity_id) {
						this.times_b_int = this.form.start_time_text
						this.times_e_int = this.form.end_time_text
						this.times_sinb_int = this.form.sign_start_time_text
						this.times_sine_int = this.form.sign_end_time_text
					} else {
						this.times_b_int = ''
						this.times_e_int = ''
						this.times_sinb_int = ''
						this.times_sine_int = ''
						this.form.sign_time = ''
						this.form.time = ''
					}
					if (this.type == 2) {
						this.form.title = ''
					}

					this.mobile = this.detail.user.mobile.slice(0, 3) + 'XXXX' + this.detail.user.mobile.slice(
						7);
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none',
						duration: 2000
					})
				}
			}).catch(error => { });
		},
		priceDo() {
			var price = this.price;
			console.log('priceDo  price:', price);
			if (price == '' || price == null || price == undefined || price == 0) {
				this.priceName = '免费';
				this.priceShow = false;
				return;
			}
			//判断金额不能小于1
			if (price < 1) {
				uni.showToast({
					title: '活动价格不能小于1.00',
					icon: 'none',
					duration: 2000,
				});
				return;
			}
			//帮忙格式化一下金额。如是正整数，则补全.00，补充位数
			if (price.indexOf('.') == -1) {
				price = price + '.00';
			}
			this.form.price = this.price;
			this.priceName = price;
			this.priceShow = false;

		},
		selectItemTuikuan() {
			this.currents = 5;
			this.form.refund_id = 3;
			this.form.refund_idn = '不支持自动退款';
		},
		Time(val) {
			console.log('Time_val:', val);
		},
		openUrl(url) {
			uni.navigateTo({
				url: url
			})
		},
		getCardInfo() {
			uni.$u.http.get('/api/school.real_name/info').then(res => {
				console.log('getCardInfo  res:', res);
				this.cardShow = res.data.status != 1 ? true : false;
				this.cardStatus = res.data.status;
			})
		},
		//选择政策
		selectItem(index, item) {
			this.currents = index;
			this.form.refund_id = item.id;
			this.form.refund_idn = item.title;
		},
		// async uploadQun(item) {
		// 	const result = await this.uploadFilePromise(item.file.url, 'user');
		// 	this.qunQrcode = result;
		// 	console.log('qunQrcode',this,qunQrcode);
		// },
		async uploadQun(item) {
			const result = await this.uploadFilePromise(item.file.url, 'user');
			console.log('uploadQun  result:', result);
			this.qunQrcode = result;
			console.log('qunQrcode', this.qunQrcode);
			this.QunQrcode1 = this.qunQrcode
			const baseUrl = 'https://qingchunta.hschool.com.cn'; // 你的域名
			this.QunQrcode1 = {
				url: baseUrl + result // 拼接完整URL
			};
			console.log('QunQrcode1', this.QunQrcode1);
			if (result.code != 1) {
				// uni.showToast({
				// 	title: result.msg,
				// 	icon: 'none',
				// 	duration: 2000
				// });
				return;
			} else {
				// uni.$u.http.post('/api/school.new_activity/edit_qrcode', {
				// 	id: this.id,
				// 	image: result.data.url,
				// }).then(res => {
				// 	if (res.code == 1) {
				// 		uni.showToast({
				// 			title: '上传成功！',
				// 			icon: 'none',
				// 			duration: 2000
				// 		});
				// 		this.getDetail();
				// 	} else {
				// 		uni.showToast({
				// 			title: res.msg,
				// 			icon: 'none',
				// 			duration: 2000
				// 		});
				// 	}
				// }).catch(error => {
				// 	uni.showToast({
				// 		title: '请求失败，请稍后再试',
				// 		icon: 'none',
				// 		duration: 2000
				// 	});
				// });
			}
		},
		//登录及发布123
		// tofb() {	
		// 	const token = uni.getStorageSync('token')
		// 	if (token) {
		// 		return true;
		// 	} else {
		// 		uni.showToast({
		// 			title: '请登录',
		// 			icon: 'none',
		// 			duration: 2000,
		// 			complete: function() {
		// 				setTimeout(function() {
		// 					uni.switchTab({
		// 						url: '/pages/my/index',
		// 					});
		// 				}, 2000);
		// 			}
		// 		});
		// 	}
		// },
		// 获取本地最近使用
		getitembq() {
			this.list = [];
			uni.setStorageSync('zjlist', JSON.stringify(this.list))
			// if (uni.getStorageSync("zjlist") != "") {
			// 	this.list = JSON.parse(uni.getStorageSync("zjlist"));
			// 	let arr = this.list.map((item) => {
			// 		return item.name
			// 	})
			// 	this.form.cate_idsName = arr.join(',');
			// } else {
			// 	uni.setStorageSync('zjlist', JSON.stringify(this.list))
			// }
		},
		//删除标签（本地）
		removebq(i) {
			this.list.splice(i, 1);
			uni.setStorageSync('zjlist', JSON.stringify(this.list))
			let arr = this.list.map((item) => {
				return item.name
			})
			this.form.cate_idsName = arr.join(',')
		},
		addbq(val, index) {
			this.current = index;
			let arrbql = uni.getStorageSync("zjlist");
			let arrbq = JSON.parse(arrbql);
			let isbq = true;
			for (let i = 0; i < arrbq.length; i++) {
				if (arrbq[i].id == val.id) {
					uni.showToast({
						title: "已选择该标签",
						icon: "none",
						duration: 2000,
					});
					return isbq = false;
				}
			}
			if (this.list.length < 3) {
				if (isbq) {
					this.list.push(val);
					// uni.setStorageSync('zjlist', JSON.stringify(this.list))
				}
			} else {
				uni.showToast({
					title: "最多添加3个标签",
					icon: "none",
					duration: 2000,
				});
			}
			let arr = this.list.map((item) => {
				return item.name
			})
			this.form.cate_idsName = arr.join(',');
			console.log('this.form.cate_idsName', this.form.cate_idsName);
		},
		dateWeeks(e) {
			return dateWeek(e);
		},
		closebq() {
			this.showPopbq = false
		},
		openBq() {
			this.showPopbq = true
		},
		// 退费规则
		getrefund_list() {
			uni.$u.http
				.get("/api/school.new_activity/refund_list", {
					params: {
						page: 1,
						limit: 100
					},
				})
				.then((res) => {
					if (res.code == 1) {
						this.refund_list = res.data.list;
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none",
							duration: 2000,
						});
					}
				})
				.catch((error) => {
					uni.showToast({
						title: "请求失败，请稍后再试",
						icon: "none",
						duration: 2000,
					});
				});
		},
		// 获取标签
		getBqList() {
			uni.$u.http
				.get("/api/school.new_activity/cate_list", {
					params: {
						page: 1,
						limit: 100
					},
				})
				.then((res) => {
					if (res.code == 1) {
						this.bqList = res.data.list;
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none",
							duration: 2000,
						});
					}
				})
				.catch((error) => {
					uni.showToast({
						title: "请求失败，请稍后再试",
						icon: "none",
						duration: 2000,
					});
				});
		},
		//同意
		handleScroll() {
			console.log('handleScroll:', 123)
			this.agreeShow = true
		},
		getTime() {
			const currentDate = new Date();
			const year = currentDate.getFullYear();
			const month = String(currentDate.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
			const day = String(currentDate.getDate()).padStart(2, '0');

			const formattedDate = `${year}-${month}-${day}`;
			return formattedDate;
		},
		//判断是否早于当前时间
		isBeforeNow(dateTimeStr) {
			// 替换空格为'T'以兼容ISO格式（部分浏览器需要）
			const isoFormattedStr = dateTimeStr.replace('  ', 'T');
			console.log('isBeforeNow——inputDate:', isoFormattedStr);
			const inputDate = new Date(isoFormattedStr);
			console.log('isBeforeNow——inputDate:', inputDate);
			// 清除秒和毫秒
			inputDate.setSeconds(0, 0);
			const now = new Date();
			console.log('isBeforeNow——now:', now);
			// 清除秒和毫秒
			now.setSeconds(0, 0);
			console.log('isBeforeNow——now:', now);
			// console.log('isBeforeNow——判断是否早于当前时间', inputDate, now);
			return inputDate < now;
		},
		isDateTimeBefore(datetimeStr1, datetimeStr2) {
			// 转换为Date对象（兼容"YYYY-MM-DD HH:mm:ss"格式）
			console.log('isDateTimeBefore_接参查看',datetimeStr1, datetimeStr2);
			const date1 = new Date(datetimeStr1.replace('  ', 'T') + 'Z');
			const date2 = new Date(datetimeStr2.replace('  ', 'T') + 'Z');
			console.log('isDateTimeBefore——date1：', date1);
			console.log('isDateTimeBefore——date2：', date2);
		
			date1.setSeconds(0, 0);
			date2.setSeconds(0, 0);
		
			return date1 <= date2; // 如果date1早于date2，返回true 
		},
		//活动开始时间
		dateShowHidden() {
			console.log('活动时间');
			this.hdIndex = 1;
			this.dateShow = true;
			const defaultTime = new Date();
			console.log('活动时间defaultTime', defaultTime);
		},
		//活动时间变化-select事件
		datefirmStartTime(e) {
			console.log('选择的ACT开始time——select：', e.time);
			this.start_time = e.time;
		},
		//活动开始时间——确定
		sureStartTime() {
			//获取当前时间
			const currentTime = dayjs().format('YYYY-MM-DD HH:mm');
			var b = currentTime + ':00';
			console.log('start_time——b', b, this.start_time);
			//判断是否this.start_time小于当前时间 用的是年月日而不是时间戳
			if (this.start_time == '' || this.start_time == null) {
				console.log('本次未选择以及form.start_time皆为空');
				//this.start_time = '';
				 this.start_time = b;
			} else {
				console.log('start_time不为空');
				if (this.isBeforeNow(this.start_time)) {
					uni.$u.toast('活动开始时间不能小于当前时间');
					return;
				}
				//报名结束时间 应早于 活动开始时间
				// 如果date1 < date2，返回true 即date1早于date2
				if (this.sign_end_time != '' && this.isDateTimeBefore(this.start_time, this.sign_end_time)) {
					console.log('判断活动开始和报名结束',this.isDateTimeBefore(this.sign_end_time, this.start_time));
					uni.$u.toast('活动开始时间不能早于报名结束时间');
					return;
				}
				//活动结束时间 要晚于 活动开始时间
				if (this.end_time != '' && this.isDateTimeBefore(this.end_time, this.start_time)) {
					console.log('判断活动开始和结束');
					uni.$u.toast('活动开始时间不能晚于活动结束时间');
					return;
				}
		
				//this.times_b = dateWeekData(this.times_b);
			}
			this.form.start_time = dateWeekData(this.start_time);
			console.log('startime:', this.start_time);
			this.dateShow = false;
			// this.checkFormValidity();
		},
		//活动开始时间取消逻辑
		dateShowCancel() {
			this.dateShow = false;
			this.start_time = '';
		},
		//活动结束时间打开
		actEndDateOpen() {
			console.log('活动结束时间');
			this.actEndShow = true;
			const defaultTime = new Date();
			// defaultTime.setHours(defaultTime.getHours() + 8);
			console.log('活动时间defaultTime', defaultTime);
			// this.defaultTime = defaultTime;
		},
		//act结束时间选择-select事件
		datefirmStartTimeEnd(e) {
			console.log('选择的ACT开始time——select：', e.time);
			this.end_time = e.time;
		},
		//活动结束时间取消
		endTimeShowCancel() {
			this.actEndShow = false;
			this.end_time = '';
		},
		//act结束时间确定事件
		sureEndTime() {
			//获取当前时间
			const currentTime = dayjs().format('YYYY-MM-DD HH:mm');
			// const currentTime = this.getCurrentTimePlus8Hours();
			var b = currentTime + ':00';
			console.log('act结束_bend', b, this.end_time);
			if (this.end_time == '' || this.end_time == null) {
				console.log('选择的活动结束时间为空');
				//判断是否this.times_b小于当前时间 用的是年月日而不是时间戳
				if (this.isBeforeNow(b)) {
					uni.$u.toast('活动结束时间不能小于当前时间');
					return;
				}
				this.end_time = b;
				// this.end_time = '';
			} else {
				console.log('判断是否this.times_b小于当前时间 用的是年月日而不是时间戳');
				if (this.isBeforeNow(this.end_time)) {
					uni.$u.toast('活动结束时间不能小于当前时间');
					return;
				}
				//报名结束时间 < 活动开始时间
				// 如果date1 < date2，返回true 
				console.log('this.start_time', this.end_time, this.start_time);
				if (this.isDateTimeBefore(this.end_time, this.start_time) && this.start_time != '') {
					uni.$u.toast('活动结束时间不能早于活动开始时间');
					return;
				}
				// this.times_b = dateWeekData(this.times_b);
				this.form.end_time = dateWeekData(this.end_time);
			}
			console.log('endtime:', this.end_time);
			// this.form.end_time = dateWeekData(this.end_time);
			// this.form.end_time = this.end_time;
			console.log('formendTime:', this.form.end_time);
			this.actEndShow = false;
			// this.checkFormValidity();
		},
		//报名开始时间打开
		birthShowHidden() {
			console.log('报名开始时间');
			this.birthShow = true;
			// 计算当前时间加8小时
			const defaultTime = new Date();
			//defaultTime.setHours(defaultTime.getHours() + 8);
			console.log('报名时间defaultTime', defaultTime);
			this.defaultTime = defaultTime;
		},
		//报名开始时间的选择
		birthSignStartConfirm(e) {
			console.log('选择的ACT开始time——select：', e.time);
			this.sign_start_time = e.time;
		},
		//报名开始时间取消逻辑
		birthShowCancel() {
			this.birthShow = false;
			this.sign_start_time = '';
			// this.form.sign_time = ''
		},
		//报名开始时间——确定事件
		sureBirthShow() {
			const currentTime = dayjs().format('YYYY-MM-DD HH:mm');
			if (this.sign_start_time == '' || this.sign_start_time == null) {
			 var b = currentTime + ':00';
				// //判断是否this.sign_start_time小于当前时间 用的是年月日而不是时间戳
				if (this.isBeforeNow(b)) {
					uni.$u.toast('报名开始时间不能早于当前时间');
					return;
				}
				 this.sign_start_time = b;
			} else {
				if (this.isBeforeNow(this.sign_start_time)) {
					uni.$u.toast('报名开始时间不能早于当前时间');
					return;
				}
				if (this.isDateTimeBefore(this.sign_end_time, this.sign_start_time, ) && this.sign_end_time != '') {
					uni.$u.toast('报名开始时间不能晚于报名结束时间');
					return;
				}
				if (this.isDateTimeBefore(this.start_time, this.sign_start_time, ) && this.start_time != '') {
					uni.$u.toast('报名开始时间不能晚于活动开始时间');
					return;
				}
				//this.times_sinb = dateWeekData(this.times_sinb);
			}
			this.form.sign_start_time = dateWeekData(this.sign_start_time);
			// this.bmIndex = 2;
			this.birthShow = false;
			this.sign_start_time = '';
			// this.checkFormValidity();
		},
		//报名结束时间打开
		signUpEndShowOpen() {
			console.log('报名结束时间');
			this.signUpEndShow = true;
			const defaultTime = new Date();
			console.log('报名时间defaultTime', defaultTime);
			this.defaultTime = defaultTime;
		},
		//报名结束时间的选择
		birthSignEndConfirm(e) {
			console.log('选择的ACT开始time——select：', e.time);
			this.sign_end_time = e.time;
		},
		//报名结束时间取消逻辑
		signUpEndShowCancel() {
			this.signUpEndShow = false;
			this.sign_end_time = '';
		},
		//报名结束时间——确定事件
		signUpSure() {
			const currentTime = dayjs().format('YYYY-MM-DD HH:mm');
			console.log('报名结束currentTime', currentTime);
			var b = currentTime + ':00';
			console.log('报名结束b', b);
			if (this.sign_end_time == '' || this.sign_end_time == null) {
				console.log('未选择——sign_end_time为空');
				//判断是否this.sign_end_time小于当前时间 用的是年月日而不是时间戳
				if (this.isBeforeNow(b)) {
					uni.$u.toast('报名结束时间不能小于当前时间');
					return;
				}
				if (this.isDateTimeBefore(this.sign_start_time, b) && this.sign_start_time != '') {
					uni.$u.toast('报名结束时间不能早于报名开始时间');
					return;
				}
				if (this.isDateTimeBefore(this.start_time, b) && this.start_time != '') {
					uni.$u.toast('报名结束时间不能晚于活动开始时间');
					return;
				}
				this.sign_end_time = b;
			} else {
				console.log('已选择——sign_end_time有值');
				//判断是否this.sign_end_time小于当前时间 用的是年月日而不是时间戳
				if (this.isBeforeNow(this.sign_end_time)) {
					uni.$u.toast('报名结束时间不能小于当前时间');
					return;
				}
				if (this.isDateTimeBefore(this.sign_end_time, this.sign_start_time) && this.sign_start_time != '') {
					uni.$u.toast('报名结束时间不能早于报名开始时间');
					return;
				}
				if (this.isDateTimeBefore(this.start_time, this.sign_end_time) && this.start_time != '') {
					uni.$u.toast('报名结束时间不能晚于活动开始时间');
					return;
				}
				// this.times_sine = dateWeekData(this.times_sine);
				// this.times_sinb = dateWeekData(this.times_sinb);
			}
			// this.checkFormValidity();
			this.form.sign_end_time = dateWeekData(this.sign_end_time);
			this.signUpEndShow = false;
			// this.form.sign_end_time = this.sign_end_time;
		},
		

		// 活动开始时间
		datefirm(e, index) {
			console.log('活动开始时间', e, e.time)
			var time = e.time;
			// var time = this.tempDefaultTime || e.time
			if (index == 1) {
				//this.form.date = time;
				this.times_b = time;
				this.times_b_int = time;
			} else {
				this.times_e = time;
				this.times_e_int = time;
				//this.dateShow = false
				this.form.time = this.times_b + ' - ' + this.times_e
			}
			this.tempDefaultTime = null; // 使用后重置
			//this.dateShow1 = true

			//this.form.date = dayjs(e.value).format('YYYY-MM-DD HH:mm:ss');
			//this.times_b = this.dateWeeks(e.value / 1000);
			//this.dateShow = false
			//this.dateShow1 = true
		},
		hdnext() {
			//获取当前时间
			const currentTime = dayjs().format('YYYY-MM-DD HH');
			// const currentTime = new Date(new Date().getTime() + 8 * 60 * 60 * 1000)

			console.log('currentTime', currentTime);
			var b = currentTime + ':00';
			if (this.times_b == '' || this.times_b == null) {
				//判断是否this.times_b小于当前时间 用的是年月日而不是时间戳
				if (this.isBeforeNow(b)) {
					uni.$u.toast('活动开始时间不能小于当前时间');
					return;
				}
				this.times_b = b;
			} else {
				if (this.isBeforeNow(this.times_b)) {
					uni.$u.toast('活动开始时间不能小于当前时间');
					return;
				}
				//报名结束时间 < 活动开始时间
				// 如果date1 < date2，返回true 
				if (this.isDateTimeBefore(this.times_b, this.times_sine_int) && this.times_sine_int != '') {
					uni.$u.toast('活动开始时间不能小于报名结束时间');
					return;
				}
				//this.times_b = dateWeekData(this.times_b);
			}
			this.hdIndex = 2;
		},
		hdok() {
			const currentTime = dayjs().format('YYYY-MM-DD HH');
			var b = currentTime + ':00';
			if (this.times_e == '' || this.times_e == null) {

				//判断是否this.times_e小于当前时间 用的是年月日而不是时间戳
				if (this.isBeforeNow(b)) {
					uni.$u.toast('活动结束时间不能小于开始时间');
					return;
				}
				if (this.isDateTimeBefore(this.times_e, this.times_b)) {
					uni.$u.toast('活动结束时间不能小于开始时间');
					return;
				}
				//报名结束时间 < 活动开始时间
				// 如果date1 < date2，返回true 
				if (this.isDateTimeBefore(this.times_e, this.times_sine_int) && this.times_sine_int != '') {
					uni.$u.toast('活动结束时间不能小于报名结束时间');
					return;
				}
				this.times_e = b;
			} else {
				if (this.isBeforeNow(this.times_e)) {
					uni.$u.toast('活动结束时间不能小于当前时间');
					return;
				}
				//报名结束时间 < 活动开始时间
				// 如果date1 < date2，返回true 
				if (this.isDateTimeBefore(this.times_e, this.times_sine_int) && this.times_sine_int != '') {
					uni.$u.toast('活动结束时间不能小于报名结束时间');
					return;
				}
				this.times_e = dateWeekData(this.times_e);
				this.times_b = dateWeekData(this.times_b);
			}
			this.form.time = this.times_b + ' - ' + this.times_e
			this.dateShow = false;
			// if(this.times_e == '' || this.times_e==null){
			// 	const currentTime = dayjs().format('YYYY-MM-DD HH');
			// 	this.times_e = dateWeekData(currentTime+'00');
			// 	this.form.time = this.times_b + ' - ' + this.times_e
			// }
			// this.dateShow=false;
		},
		datecel(e) {
			this.dateShow = false
		},
		datecel1(e) {
			this.dateShow1 = false
		},
		// 报名开始日期
		birthConfirm(e, index) {
			// this.form.birth = dayjs(e.value).format('YYYY-MM-DD HH:mm:ss');
			// this.times_sinb = this.dateWeeks(e.value / 1000);
			// this.birthShow = false
			// this.birthShow1 = true
			console.log('报名时间：', e)
			var time = e.time;
			if (index == 1) {
				//this.form.date = time;
				this.times_sinb = time;
				this.times_sinb_int = time;
				console.log('报名时间11：', e)
			} else {
				this.times_sine = time;
				this.times_sine_int = time;
				//this.dateShow = false
				console.log('报名时间22：', this.times_sine, this.times_sine_int)
				this.form.sign_time = this.times_sinb + ' - ' + this.times_sine
			}
		},
		//报名时间取消逻辑
		bmnext() {
			//获取当前时间
			console.log('校验时间');
			const currentTime = dayjs().format('YYYY-MM-DD HH');
			if (this.times_sinb == '' || this.times_sinb == null) {
				var b = currentTime + ':00';
				//判断是否this.times_sinb小于当前时间 用的是年月日而不是时间戳
				if (this.isBeforeNow(b)) {
					uni.$u.toast('报名开始时间不能小于当前时间');
					return;
				}
				this.times_sinb = b;
			} else {
				if (this.isBeforeNow(this.times_sinb)) {
					uni.$u.toast('报名开始时间不能小于当前时间');
					return;
				}
				if (this.isDateTimeBefore(this.times_b_int, this.times_sinb_int) && this.times_b_int != '') {
					uni.$u.toast('报名开始时间不能大于活动开始时间');
					return;
				}
				//this.times_sinb = dateWeekData(this.times_sinb);
			}
			this.bmIndex = 2;
		},
		bmok() {
			const currentTime = dayjs().format('YYYY-MM-DD HH');
			var b = currentTime + ':00';
			if (this.times_sine == '' || this.times_sine == null) {

				//判断是否this.times_sine小于当前时间 用的是年月日而不是时间戳
				if (this.isBeforeNow(b)) {
					uni.$u.toast('报名结束时间不能小于当前时间');
					return;
				}
				if (this.isDateTimeBefore(this.times_sine, this.times_sinb)) {
					uni.$u.toast('活动结束时间不能小于开始时间');
					return;
				}
				if (this.isDateTimeBefore(this.times_b_int, this.times_sine_int) && this.times_b_int != '') {
					uni.$u.toast('报名结束时间不能大于活动开始时间');
					return;
				}
				this.times_sine = b;
			} else {
				if (this.isBeforeNow(this.times_sine)) {
					uni.$u.toast('报名结束时间不能小于当前时间');
					return;
				}
				// 如果date1早于date2，返回true 
				if (this.isDateTimeBefore(this.times_sine, this.times_sinb)) {
					uni.$u.toast('报名结束时间不能小于开始时间');
					return;
				}
				if (this.isDateTimeBefore(this.times_b_int, this.times_sine_int) && this.times_b_int != '') {
					uni.$u.toast('报名结束时间不能大于活动开始时间');
					return;
				}
				this.times_sine = dateWeekData(this.times_sine);
				this.times_sinb = dateWeekData(this.times_sinb);
			}
			this.form.sign_time = this.times_sinb + ' - ' + this.times_sine
			this.birthShow = false;
		},
		birthCancel() {
			this.birthShow = false
		},
		birthCancel1() {
			this.birthShow1 = false
		},
		close() {
			this.show = false;
			clearInterval(this.timer);
		},
		closetuikuan() {
			this.tuikuan = false
		},
		open() {
			this.show = true
			// setTimeout(() => {
			// 	this.agree = true
			// }, 5000)
		},
		opentuikuan() {
			this.tuikuan = true;
			//this.form.refund_id = 1;
			//this.form.refund_idn = '随时退';
		},
		change() {
			this.agree = true;
			this.agreeAdd = true;
			this.show = false
		},
		// 选择机构地址

		protocolClick(tag) {
			console.log('editDraft');
			//timeLog 开始倒计时
			// this.timeLog = 5;
			// this.timer = setInterval(() => {
			// 	this.timeLog--;
			// 	if (this.timeLog === 0) {
			// 		this.agreeShow=true;
			// 		clearInterval(this.timer);
			// 	}
			// }, 1000);
			this.show = true
		},
		// 获取入驻协议文章
		getAgreement() {
			uni.$u.http.get('/api/index/agreement', {
				params: {

				}
			}).then(res => {
				if (res.code == 1) {
					this.Negotiate = (res.data.commitment_activity).replace(/\<img/gi,
						'<img style="max-width:100%;height:auto" ');
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none',
						duration: 2000
					});
				}
			}).catch(error => {
				uni.showToast({
					title: '请求失败，请稍后再试',
					icon: 'none',
					duration: 2000
				});
			});
		},
		choose() {
			console.log('choose', 11111)
			const that = this
			uni.chooseLocation({
				success: function (res) {
					console.log('choose  res:', res)
					that.form.address = res.name
					// that.form.address_detail = res.address
					that.form.latitude = res.latitude
					that.form.longitude = res.longitude
				},
				fail: function (rot) {
					console.log('choose  rot:', rot)
				}
			});
		},


		// 回调参数为包含columnIndex、value、values
		confirm(e) {
			console.log('confirm', e.value)
			this.form.cut = e.value[0]
			this.fileList1 = [],
				this.list1 = '',
				this.name = ''
		},


		oversize(e) {
			this.$u.toast("请传1MB以内大小的图片！");
			return false;
		},

		// 删除图片
		deletePic(event) {
			const {
				name,
				index
			} = event;

			if (index >= 0 && index < this[`fileList${name}`].length) {
				this[`fileList${name}`].splice(index, 1);


				if (name === '1') {
					console.log('删除照片？');
					//单张
					// this.list1 = this.fileList1.length > 0 ? this.fileList1[0].url : '';

					// if (this.id || this.type == 1 || this.type == 2) {
					// 	this.list1 = this.fileList1.map(url => {
					// 		// 使用URL对象方法
					// 		// const urlObj = new URL(url);
					// 		const urlObj = url.url.split('qingchunta.hschool.com.cn')[1]
					// 		console.log('urlObj', urlObj);
					// 		return urlObj;
					// 	}).join(',');
					// } else {
					// 	this.list1 = this.fileList1.map(item => item.url).join(',');
					// }
					console.log('Updated 删除图片 fileList1:', this.fileList1);
					console.log('Updated 删除图片 list1:', this.list1);
				}


				// 确保对应的 list 字段是一个数组
				let list = this[`list${name}`];
				console.log('删除照片1', list);
				if (!Array.isArray(list)) {
					console.warn(`list${name} is not an array, skipping splice operation`);
				} else {
					list.splice(index, 1);
					console.log(`Updated list${name}:`, list);
				}
			} else {
				console.error('Invalid index');
			}
		},
		// 新增图片
		async afterRead(event) {
			console.log('afterRead', this.fileList1);
			console.log('afterRead', this.list1);
			// this.fileList1 = this.fileList1.forEach(url => {
			// 	console.log('url',url);
			// 	url.url = url.url.split('qingchunta.hschool.com.cn')[1]

			// });
			// console.log('afterRead222', this.fileList1);

			const lists = [].concat(event.file);
			console.log('新增照片List1', lists);
			if (this.id || this.type == 1 || this.type == 2) {

				const lists = this.fileList1.concat(event.file);
				console.log('id type', lists);
			} else {
				const lists = [].concat(event.file);
				console.log('新增照片List1', lists);
			}
			console.log('新增照片  list:', lists);
			let fileListLen = this[`fileList${event.name}`].length;
			console.log('新增照片List2', fileListLen);
			let categoryMap = [{
				category: 'user'
			},
			{
				category: 'user'
			},
			{
				category: 'user'
			},
			{
				category: 'cert'
			},
			{
				category: 'cert'
			},
			{
				category: 'cert'
			},
			];
			lists.map((item) => {
				this[`fileList${event.name}`].push({
					...item,
					status: 'uploading',
					message: '上传中'
				});
			});
			console.log('新增照片List3', lists);
			for (let i = 0; i < lists.length; i++) {
				const result = await this.uploadFilePromise(lists[i].url, categoryMap[event.name - 1].category);
				if (result == '') {
					this.fileList1 = [];
					return;
				}
				let item = this[`fileList${event.name}`][fileListLen];
				this[`fileList${event.name}`].splice(fileListLen, 1, Object.assign(item, {
					status: 'success',
					message: '',
					url: result
				}));
				console.log(this[`fileList${event.name}`], ` this[\`fileList${event.name}\`]`)
				fileListLen++;
				// 更新对应的list字段
				if (event.name === '1') {
					console.log('event.name', this.fileList1);
					// this.list1 = this.fileList1[0]?.url || ''; //单张
					// for (let i = 0; i < this.fileList1.length; i++) {
					// 	if (/^https?:\/\//i.test(this.fileList1[i].url)) {
					// 		this.fileList1[i].url = this.fileList1[i].url.replace(/^https?:\/\/[^/]+/, '');
					// 		console.log('this.fileList1[i].url', this.fileList1[i].url);

					// 	} 


					// }
					if (this.id || this.type == 1 || this.type == 2) {
						this.list1 = this.fileList1.map(url => {
							// 使用URL对象方法
							// const urlObj = new URL(url);
							const urlObj = url.url.split('qingchunta.hschool.com.cn')[1]
							console.log('urlObj', urlObj);
							return urlObj;
						});
					} else {
						this.list1 = this.fileList1.map(item => item.url);
					}
					console.log('this.fileList1', this.fileList1);
					this.list1 = this.fileList1.map(item => item.url);
					console.log('this.list1', this.list1);
				}
			}
		},
		uploadFilePromise(url, category) {
			console.log('category', category)
			return new Promise((resolve, reject) => {
				let a = uni.uploadFile({
					url: 'https://naweigetetest2.hschool.com.cn/api/common/upload', // 仅为示例，非真实的接口地址
					filePath: url,
					name: 'file',
					formData: {
						user: 'test',
						category: category
					},
					header: {
						"token": uni.getStorageSync("token")
					},
					success: (res) => {
						var js = JSON.parse(res.data);
						console.log('js.data.errcode:', js.data.errcode);
						if (js.data.errcode == '30002') {
							uni.showToast({
								title: '请登录...',
								icon: 'none',
								duration: 1000
							});
							setTimeout(() => {
								uni.switchTab({
									url: '/pages/my/index',
								})
							}, 1000)
							resolve('');
						}
						resolve(js.data.url);
					},
					fail: (err) => {
						reject(err);
					}
				});
			});
		},
		// 查询状态
		// -1未申请 0待审核 1审核通过 2审核失败
		searchStatus() {
			uni.$u.http.get('/api/school/shop/auth_info', {
				params: {}
			}).then(res => {
				if (res.code == 1) {
					this.apply_info = res.data.apply_info
					this.State = res.data.type
					console.log('res.data.type:', res.data.type)
					if (res.data.auth_status == 2) {
						this.status = 2
						this.reason = res.data.reason


					} else if (res.data.auth_status == 0) {
						this.status = 0
					} else if (res.data.auth_status == 1) {
						this.status = 1
					}
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none',
						duration: 2000
					});
				}
			}).catch(error => {
				uni.showToast({
					title: '请求失败，请稍后再试',
					icon: 'none',
					duration: 2000
				});
			});
		},

		// 验证电话号码格式的方法
		validateTel(tel) {
			// 这里使用一个简单的正则表达式来验证电话号码格式
			const telRegex = /^1[3-9]\d{9}$/; // 假设是中国大陆的手机号码格式
			return telRegex.test(tel);
		},

		//编辑草稿
		editDraft() {
			console.log('草稿');
			let url = '/api/school.newactivity.activity_drafts/edit';
			let params = {};
			let hdtime = ''
			let bmtime = ''
			if (this.list.length != 0) {
				this.form.cate_ids = this.list.map(item => item.id).join(',');
			}
			if (this.times_b_int && this.times_e_int) {
				hdtime = this.times_b_int + ' - ' + this.times_e_int;
			}
			if (this.times_sinb_int && this.times_sine_int) {
				bmtime = this.times_sinb_int + ' - ' + this.times_sine_int;
			}
			if (this.list.length != 0) {
				this.form.cate_ids = this.list.map(item => item.id).join(',');
			}
			if (this.form.start_time != '' && this.form.start_time != null) {
				this.form.start_time = this.form.start_time + ':00';
			}
			console.log('提交时的活动开始时间：', this.form.start_time);
			if (this.form.end_time != '' && this.form.end_time != null) {
				this.form.end_time = this.form.end_time + ':00';
			}
			console.log('提交时的活动结束时间：', this.form.end_time);
			if (this.form.sign_start_time != '' && this.form.sign_start_time != null) {
				this.form.sign_start_time = this.form.sign_start_time + ':00';
			}
			console.log('提交时的报名开始时间：', this.form.sign_start_time);
			if (this.form.sign_end_time != '' && this.form.sign_end_time != null) {
				this.form.sign_end_time = this.form.sign_end_time + ':00';
			}
			console.log('提交时的报名结束时间：', this.form.sign_end_time);
			
			console.log('params，this.list', this.list, this.form.cate_ids);
			// let hdtime = this.times_b_int + ' - ' + this.times_e_int;
			// let bmtime = this.times_sinb_int + ' - ' + this.times_sine_int;
			params = {
				ids: this.draftId,
				title: this.form.title,
				cate_ids: this.form.cate_ids,
				content: this.form.content,
				// refund_id: 1,
				refund_id: this.form.refund_id,
				price: this.form.price == '' ? 0 : this.form.price,
				stock: this.form.stock,
				// sign_time: bmtime || '',
				// time: hdtime || '',
				images: this.list1,
				longitude: this.form.longitude,
				latitude: this.form.latitude,
				address: this.form.address,
				address_detail: this.form.address_detail,
				image: this.qunQrcode,
				mobile:this.form.mobile,
				feel: this.form.feel,
				offline: this.form.offline,
				start_time: this.form.start_time || '',
				end_time: this.form.end_time || '',
				sign_start_time: this.form.sign_start_time || '',
				sign_end_time: this.form.sign_end_time || '',
			}

			// 转换为相对路径
			if (params.image.url) {
				console.log('params.image', params.image.url);
				if (typeof params.image === 'object' && params.image.url) {
					console.log('params.image222', params.image);
					params.image = params.image.url; // 如果是对象，取 url 字段
				}
				params.image = params.image.replace(/^https?:\/\/[^/]+/, '');
				console.log('params', params.image);
			} else {
				params.image = ''
			}


			console.log('草稿1');
			if (params.images != '') {
				for (let i = 0; i < params.images.length; i++) {
					if (/^https?:\/\//i.test(params.images[i])) {
						params.images[i] = params.images[i].replace(/^https?:\/\/[^/]+/, '');
						console.log('params.images[i].url', params.images[i]);
					} else {
						params.images[i] = params.images[i]
					}
				}
				console.log('params.images排查数组的url的路径', params.images);
				params.images = params.images.join(',');
			}

			console.log('草稿1');
			uni.$u.http.post(url, params).then(res => {
				console.log('草稿2');
				if (res.code == 1) {
					this.baoDraftShow = true
					//置空
					this.fileList1 = [];
					this.agree = false;
					this.list1 = '';
					this.list = [];
					this.price = '';
					this.priceName = '免费';
					this.qunQrcode = '';
					this.times_b = '';
					this.times_sinb = '';
					this.times_b_int = '';
					this.times_sinb_int = '';
					this.times_e = '';
					this.times_e_int = '';
					this.times_sine = '';
					this.times_sine_int = '';
					this.payTab=-1;
					this.start_time= '';
					this.end_time='';
					this.sign_start_time='';
					this.sign_end_time='';
					this.form = {
						feel: '',
						offline: '',
						mobile:'',
						cate_ids: '',
						// 活动分类名字
						cate_idsName: "",
						content: '',
						refund_id: '',
						refund_idn: '',
						price: 1,
						stock: '',
						sign_time: '',
						time: '',
						images: '',
						title: '',
						address: '',
						latitude: '',
						longitude: '',
						address_detail: '', //详细位置
						date: '', //活动开始时间
						date1: '', //活动结束时间
						birth: '', //报名开始日期
						birth1: '', //报名结束日期
						start_time: '',
						end_time: '',
						sign_start_time: '',
						sign_end_time: '',
					}
					// setTimeout(function() {
					// 	// uni.navigateTo({
					// 	// 	url: "/packageA/my/orderList"
					// 	// })
					// 	uni.navigateBack()
					// }, 1000);
				} else {
					this.$u.toast(res.msg);
					// uni.showToast({
					// 	title: res.msg,
					// 	icon: 'none',
					// 	duration: 2000
					// });
				}
			}).catch(error => {
				uni.showToast({
					title: error.msg,
					icon: 'none',
					duration: 2000
				});
			});
		},
		//编辑草稿完成弹框关闭
		baoDraftCancel() {
			this.baoDraftShow = false
			uni.navigateBack()
		},

		apply() {
			console.log('apply', this.cardShow, this.cardStatus);
			console.log('apply', this.form, this.form.cate_ids, this.form.image, this.qunQrcode);
			if (this.cardStatus == -1) {
				this.cardShow = true;
				return;
			}
			let url = '/api/school.new_activity/add';
			let urlEdit = '/api/school.new_activity/edit'
			let params = {};
			console.log('apply', this.form, this.form.cate_ids, this.form.image, this.qunQrcode);

			if (this.qunQrcode == '' || this.qunQrcode == null) {
				uni.showToast({
					title: '请上传群二维码！',
					icon: 'none',
					duration: 2000
				});
				return;
			}// 校验详细地址
			if (this.form.address_detail == '') {
				uni.showToast({
					title: '请输入详细地址！',
					icon: 'none',
					duration: 2000
				});
				return;
			}
			// 校验位置
			if (this.form.address == '') {
				uni.showToast({
					title: '请选择位置！',
					icon: 'none',
					duration: 2000
				});
				return;
			}
			// 校验标题
			if (this.form.title == '') {
				uni.showToast({
					title: '请输入标题！',
					icon: 'none',
					duration: 2000
				});
				return;
			}
			// 校验详情
			if (this.form.content == '') {
				uni.showToast({
					title: '请输入详情！',
					icon: 'none',
					duration: 2000
				});
				return;
			}
			// 校验照片
			if (this.list1 == '') {
				uni.showToast({
					title: '请上传活动照片！',
					icon: 'none',
					duration: 2000
				});
				return;
			}

			// 校验照片至少上传3张
			if (this.fileList1.length < 3) {
				uni.showToast({
					title: '活动照片至少三张！',
					icon: 'none',
					duration: 2000
				});
				return;
			}

			// 校验金额
			// if (this.form.price == '') {
			// 	uni.showToast({
			// 		title: '请输入金额！',
			// 		icon: 'none',
			// 		duration: 2000
			// 	});
			// 	return;
			// }
			// 校验退款政策
			if (this.form.refund_id == '') {
				uni.showToast({
					title: '请选择退款政策！',
					icon: 'none',
					duration: 2000
				});
				return;
			}
			// 校验人数
			if (this.form.stock == '' || this.form.stock < 1) {
				uni.showToast({
					title: '请输入正确的活动人数！',
					icon: 'none',
					duration: 2000
				});
				return;
			}
			// 校验报名结束时间
			if (this.form.sign_end_time == '') {
				uni.showToast({
					title: '请选择报名结束时间！',
					icon: 'none',
					duration: 2000
				});
				return;
			}
			
			// 校验活动结束时间
			if (this.form.end_time == '') {
				uni.showToast({
					title: '请选择活动结束时间！',
					icon: 'none',
					duration: 2000
				});
				return;
			}
			if (this.qunQrcode == '' || this.qunQrcode == null) {
				uni.showToast({
					title: '请上传群二维码！',
					icon: 'none',
					duration: 2000
				});
				return;
			}
			if (this.payTab == -1) {
				uni.showToast({
					title: '请选择付费方式！',
					icon: 'none',
					duration: 2000
				});
				return;
			}
			if (this.payTab == 1 && (this.form.price == '' || this.form.price == 0 || this.form.price == null)) {
				uni.showToast({
					title: '请填写活动价格！',
					icon: 'none',
					duration: 2000
				});
				return;
			}
			console.log('time', this.form.sign_time, this.form.time);
			console.log('hdtime', this.times_b_int, this.times_e_int);
			console.log('hdtime', this.times_b_int, this.times_e_int);
			let hdtime = this.times_b_int + ' - ' + this.times_e_int;
			let bmtime = this.times_sinb_int + ' - ' + this.times_sine_int;
			if (this.form.start_time.length == 11 && this.form.start_time != '' && this.form.start_time != null) {
				this.form.start_time = this.form.start_time + ':00';
			}
			console.log('提交时的活动开始时间：', this.form.start_time);
			
			if(this.form.end_time.length == 11 && this.form.end_time != '' && this.form.end_time != null) {
				this.form.end_time = this.form.end_time + ':00';
			}
			console.log('提交时的活动结束时间：', this.form.end_time);
			
			if (this.form.sign_start_time.length == 11 && this.form.sign_start_time != '' && this.form.sign_start_time != null) {
				this.form.sign_start_time = this.form.sign_start_time + ':00';
			}
			console.log('提交时的报名开始时间：', this.form.sign_start_time);
			
			if(this.form.sign_end_time.length == 11 && this.form.sign_end_time != '' && this.form.sign_end_time != null) {
				this.form.sign_end_time = this.form.sign_end_time + ':00';
			}
			console.log('提交时的报名结束时间：', this.form.sign_end_time);
			
			// if (this.list.length != 0) {
			// 	this.form.cate_ids = this.list.map(item => item.id).join(',');
			// 	console.log('params，this.list',this.list,this.form.cate_ids);
			// }
			this.form.cate_ids = this.list.map(item => item.id).join(',');
			console.log('params，this.list', this.list, this.form.cate_ids);
			console.log('formparams', this.form, this.form.cate_ids);
			console.log('images', this.form.images);
			params = {
				title: this.form.title,
				cate_ids: this.form.cate_ids,
				content: this.form.content,
				// refund_id: 1,
				refund_id: this.form.refund_id,
				price: this.form.price == '' ? 0 : this.form.price,
				stock: this.form.stock,
				// sign_time: bmtime,
				// time: hdtime,
				// time:this.form.time,
				// sign_time: this.form.sign_time,
				images: this.list1,
				longitude: this.form.longitude,
				latitude: this.form.latitude,
				address: this.form.address,
				address_detail: this.form.address_detail,
				image: this.qunQrcode,
				ids: this.id,
				mobile:this.form.mobile,
				feel: this.form.feel,
				offline: this.form.offline,
				start_time: this.form.start_time,
				end_time: this.form.end_time,
				sign_start_time: this.form.sign_start_time,
				sign_end_time: this.form.sign_end_time,
			}
			console.log('params00', params);
			console.log('params01', this.id);
			console.log('params01', this.original_activity_id);
			// debugger
			if (typeof params.image === 'object' && params.image.url) {
				params.image = params.image.url; // 如果是对象，取 url 字段
			}
			// 转换为相对路径
			params.image = params.image.replace(/^https?:\/\/[^/]+/, '');
			console.log('params', params.image);

			console.log('草稿1');
			for (let i = 0; i < params.images.length; i++) {
				if (/^https?:\/\//i.test(params.images[i])) {
					params.images[i] = params.images[i].replace(/^https?:\/\/[^/]+/, '');
					console.log('params.images[i].url', params.images[i]);
				} else {
					params.images[i] = params.images[i]
				}
			}
			console.log('params.images排查数组的url的路径', params.images);
			params.images = params.images.join(',');

			uni.$u.http.post(url, params).then(res => {
				if (res.code == 1) {
					//置空
					this.fileList1 = [];
					this.agree = false;
					this.list1 = '';
					this.price = '';
					this.priceName = '免费';
					this.qunQrcode = '';
					this.times_b = '';
					this.times_sinb = '';
					this.times_b_int = '';
					this.times_sinb_int = '';
					this.times_e = '';
					this.times_e_int = '';
					this.times_sine = '';
					this.times_sine_int = '';
					this.payTab = -1;
					this.start_time= '';
					this.end_time='';
					this.sign_start_time='';
					this.sign_end_time='';
					this.form = {
						feel: '',
							offline: '',
						mobile:'',
						cate_ids: '',
						// 活动分类名字
						cate_idsName: "",
						content: '',
						refund_id: '',
						refund_idn: '',
						price: 0,
						stock: '',
						sign_time: '',
						time: '',
						images: '',
						title: '',
						address: '',
						latitude: '',
						longitude: '',
						address_detail: '', //详细位置
						date: '', //活动开始时间
						date1: '', //活动结束时间
						birth: '', //报名开始日期
						birth1: '', //报名结束日期
						start_time: '',
						end_time: '',
						sign_start_time: '',
						sign_end_time: '',
					}

					uni.showToast({
						title: '发布成功！',
						icon: 'none',
						duration: 2000,
					});
					setTimeout(function () {
						uni.navigateBack()
						// uni.navigateTo({
						// 	url: "/packageA/my/orderList"
						// })
					}, 1000);
				} else {
					this.$u.toast(res.msg);
					// uni.showToast({
					// 	title: res.msg,
					// 	icon: 'none',
					// 	duration: 2000
					// });
				}
			}).catch(error => {
				uni.showToast({
					title: error.msg,
					icon: 'none',
					duration: 2000
				});
			});


			// //编辑
			// if (this.id) {
			// 	console.log('编辑', params.images);
			// 	for (let i = 0; i < params.images.length; i++) {
			// 		if (/^https?:\/\//i.test(params.images[i])) {
			// 			params.images[i] = params.images[i].replace(/^https?:\/\/[^/]+/, '');
			// 			console.log('params.images[i].url', params.images[i]);
			// 		} else {
			// 			params.images[i] = params.images[i]
			// 		}
			// 	}
			// 	console.log('params.images排查数组的url的路径', params.images);
			// 	params.images = params.images.join(',');
			// 	// params.images = params.images.map(url => {
			// 	// 	// 使用URL对象方法
			// 	// 	// const urlObj = new URL(url);
			// 	// 	const urlObj = url.split('qingchunta.hschool.com.cn')[1]
			// 	// 	console.log('urlObj', urlObj);
			// 	// 	return urlObj;
			// 	// }).join(',');
			// 	console.log('params', params.images);
			// 	// params.image = new URL(params.image).pathname ;
			// 	// params.image = params.image.replace(/^https?:\/\/[^/]+/, '')
			// 	// 确保 params.image 是字符串
			// 	if (typeof params.image === 'object' && params.image.url) {
			// 		params.image = params.image.url; // 如果是对象，取 url 字段
			// 	}
			// 	// 转换为相对路径
			// 	params.image = params.image.replace(/^https?:\/\/[^/]+/, '');
			// 	console.log('params', params.image);

			// 	uni.$u.http.post(urlEdit, params).then(res => {
			// 		if (res.code == 1) {
			// 			//置空
			// 			this.fileList1 = [];
			// 			this.agree = false;
			// 			this.list1 = '';
			// 			this.price = '';
			// 			this.priceName = '免费';
			// 			this.qunQrcode = '';
			// 			this.form = {

			// 				cate_ids: '',
			// 				// 活动分类名字
			// 				cate_idsName: "",
			// 				content: '',
			// 				refund_id: '',
			// 				refund_idn: '',
			// 				price: 1,
			// 				stock: '',
			// 				sign_time: '',
			// 				time: '',
			// 				images: '',
			// 				title: '',
			// 				address: '',
			// 				latitude: '',
			// 				longitude: '',
			// 				address_detail: '', //详细位置
			// 				date: '', //活动开始时间
			// 				date1: '', //活动结束时间
			// 				birth: '', //报名开始日期
			// 				birth1: '', //报名结束日期
			// 			}

			// 			uni.showToast({
			// 				title: '修改成功！',
			// 				icon: 'none',
			// 				duration: 2000,
			// 			});
			// 			setTimeout(function() {
			// 				uni.navigateBack()
			// 				// uni.navigateTo({
			// 				// 	url: "/packageA/my/orderList"
			// 				// })
			// 			}, 1000);
			// 		} else {
			// 			this.$u.toast(res.msg);
			// 			uni.showToast({
			// 				title: res.msg,
			// 				icon: 'none',
			// 				duration: 2000
			// 			});
			// 		}
			// 	}).catch(error => {
			// 		uni.showToast({
			// 			title: error.msg,
			// 			icon: 'none',
			// 			duration: 2000
			// 		});
			// 	});
			// } else {
			// 	console.log('新增', params.images);
			// 	if (this.type == 1 || this.type == 2) {
			// 		console.log('新增11', params.images);
			// 		for (let j = 0; j < params.images.length; j++) {
			// 			if (/^https?:\/\//i.test(params.images[j])) {
			// 				params.images[j] = params.images[j].replace(/^https?:\/\/[^/]+/, '');
			// 				console.log('params.images[i].url', params.images[j]);
			// 			}
			// 		}
			// 		params.images = params.images.join(',')
			// 		console.log('params.images排查数组的url的路径', params.images);
			// 		// params.images = params.images.map(url => {
			// 		// 	// 使用URL对象方法
			// 		// 	// const urlObj = new URL(url);
			// 		// 	const urlObj = url.split('qingchunta.hschool.com.cn')[1]
			// 		// 	console.log('urlObj', urlObj);
			// 		// 	return urlObj;
			// 		// }).join(',');
			// 		console.log('params', params.images);
			// 		// params.image = new URL(params.image).pathname ;
			// 		// params.image = params.image.replace(/^https?:\/\/[^/]+/, '')
			// 		// 确保 params.image 是字符串
			// 		if (typeof params.image === 'object' && params.image.url) {
			// 			params.image = params.image.url; // 如果是对象，取 url 字段
			// 		}
			// 		// 转换为相对路径
			// 		params.image = params.image.replace(/^https?:\/\/[^/]+/, '');
			// 		console.log('params', params.image);
			// 	}

			// 	uni.$u.http.post(url, params).then(res => {
			// 		if (res.code == 1) {
			// 			//置空
			// 			this.fileList1 = [];
			// 			this.agree = false;
			// 			this.list1 = '';
			// 			this.price = '';
			// 			this.priceName = '免费';
			// 			this.qunQrcode = '';
			// 			this.form = {

			// 				cate_ids: '',
			// 				// 活动分类名字
			// 				cate_idsName: "",
			// 				content: '',
			// 				refund_id: '',
			// 				refund_idn: '',
			// 				price: 1,
			// 				stock: '',
			// 				sign_time: '',
			// 				time: '',
			// 				images: '',
			// 				title: '',
			// 				address: '',
			// 				latitude: '',
			// 				longitude: '',
			// 				address_detail: '', //详细位置
			// 				date: '', //活动开始时间
			// 				date1: '', //活动结束时间
			// 				birth: '', //报名开始日期
			// 				birth1: '', //报名结束日期
			// 			}

			// 			uni.showToast({
			// 				title: '发布成功！',
			// 				icon: 'none',
			// 				duration: 2000,
			// 			});
			// 			setTimeout(function() {
			// 				uni.navigateBack()
			// 				// uni.navigateTo({
			// 				// 	url: "/packageA/my/orderList"
			// 				// })
			// 			}, 1000);
			// 		} else {
			// 			this.$u.toast(res.msg);
			// 			// uni.showToast({
			// 			// 	title: res.msg,
			// 			// 	icon: 'none',
			// 			// 	duration: 2000
			// 			// });
			// 		}
			// 	}).catch(error => {
			// 		uni.showToast({
			// 			title: error.msg,
			// 			icon: 'none',
			// 			duration: 2000
			// 		});
			// 	});
			// }


		}
	}
}
</script>

<style lang="scss" scoped>
.allbg {
	position: fixed;
	height: 100%;
	width: 100%;
}

/* 添加新样式用于内部可滚动区域 */
.scroll-container {
	margin: 0 auto;
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 20px;
	z-index: 1;
	/* 确保在.allbg之上 */
	overflow: visible;
	/* 允许内容溢出 */
	margin-top: 50rpx;
	margin-bottom: 250rpx;
}

.title_logo {
	width: 690rpx;
	margin-top: 30rpx;
	display: flex;
}

.backImg {
	height: 100vh;
	// background: linear-gradient(to bottom, #F1F2F8 0%, #F1F2F8 50%, #FFFFFF 100%);
	width: 750rpx;
	background-color: #f7f7f7;
	// background-image: url("https://naweigetetest2.hschool.com.cn/dyqc/bgx2.png");
	// background-size: 100%;
	// background-repeat: no-repeat;
}

.w-100 {
	width: 100%;
}

.flex {
	display: flex;
}

.justify-center {
	justify-content: center;
}

.space-between {
	justify-content: space-between;
}

.align-items {
	align-items: center;
}

.flex-column {
	flex-flow: column;
}

.justify-start {
	justify-content: start;
}

.mar-top-30 {
	margin-top: 30rpx;
}

.box {
	width: 690rpx;

	.title {
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 32rpx;
		color: #C0C0C0;
		margin: 42rpx 0 24rpx 0;

	}

	.reason {
		width: 100%;
		background: #FFFFFF;
		border-radius: 20rpx 20rpx 20rpx 20rpx;
		margin-top: 32rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 36rpx;
		color: #343434;
		line-height: 36rpx;
		padding: 30rpx;
		width: 630rpx;
		letter-spacing: 4.5rpx;
	}

	.first {
		width: 100%;
		// height: 266rpx;
		background: #FFFFFF;
		border-radius: 20rpx 20rpx 20rpx 20rpx;
		// margin-top: 32rpx;

		.row {
			width: 642rpx;
			margin-top: 7rpx;
			justify-content: space-between;

			.label {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 28rpx;
				color: #343434;
				line-height: 32rpx;
				width: 160rpx;
			}

			.pic_view {
				width: 190rpx;
				height: 190rpx;
				background: #F7F7F7;
				border-radius: 10rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				flex-flow: column;

				.texts {
					margin-top: 20rpx;
					font-size: 24rpx;
					color: #3D3D3D;
				}
			}

			.row-right {
				display: flex;
				justify-content: flex-end;
				align-items: center;
			}
		}

		.line-row {
			margin-top: 25rpx;
			width: 642rpx;
			height: 1rpx;
			background: #F0F0F0;
			border-radius: 0rpx 0rpx 0rpx 0rpx;
		}
	}

	::v-deep .plasty {
		text-align: right;
		z-index: 10;
		color: #9c9c9c;
	}

	::v-deep .plasty_c {
		text-align: right;
		color: #FF4810;
	}

	::v-deep .bttop {
		font-size: 32rpx;
		color: #9C9C9C;
	}

	.second {
		width: 690rpx;
		// height: 340rpx;
		background: #FFFFFF;
		border-radius: 20rpx 20rpx 20rpx 20rpx;
		margin-top: 32rpx;
		margin-bottom: 32rpx;

		::v-deep .bttops {
			font-size: 26rpx;
			color: #9C9C9C;
		}
	}

	.third {
		width: 690rpx;

		.header {
			margin: 42rpx 0 24rpx 0;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 28rpx;
			color: #343434;
			line-height: 32rpx;
		}
	}
}

.textarea_fb {
	::v-deep .u-textarea {
		height: 100%;
		padding: 0;
		border: none;
		font-size: 26rpx;
		color: #9C9C9C;
	}

	::v-deep .bttextarea {
		font-size: 24rpx;
		color: #9C9C9C;
	}
}


.shenfen ::v-deep .u-upload .u-upload__wrap__preview__image {
	width: 196rpx !important;
	height: 196rpx !important;
}

.bottom {
	width: 750rpx;
	height: 250rpx;
	background: #FFFFFF;
	// box-shadow: 0rpx -6rpx 12rpx 0rpx rgba(111, 190, 255, 0.1);
	border-radius: 0rpx 0rpx 0rpx 0rpx;
	bottom: 10rpx;
	position: fixed;
	z-index: 10;

	span {
		width: 642rpx;
		height: 80rpx;
		background: #323232;
		border-radius: 401rpx 401rpx 401rpx 401rpx;
		font-family: YouSheBiaoTiHei;
		font-weight: 400;
		font-size: 36rpx;
		color: #BBFC5B;
		line-height: 32rpx;
		margin: 0 auto;
		margin-top: 30rpx;
	}

	.btns {
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-top: 30rpx;

		.saveDraft {
			width: 235rpx;
			height: 90rpx;
			border: 2rpx solid #323232;
			background-color: #ffffff;
			text-align: center;
			color: #323232;
			font-family: YouSheBiaoTiHei;
			font-size: 36rpx;
			font-weight: 400;
			border-radius: 50rpx;
			margin: 0 auto;
			display: grid;
			align-items: center;
		}

		.submitPublish {
			margin-left: 20rpx;
			width: 435rpx;
			height: 90rpx;
			// border: 2rpx solid #323232;
			background-color: #323232;
			color: #BBFC5B;
			font-family: YouSheBiaoTiHei;
			font-size: 36rpx;
			font-weight: 400;
			text-align: center;
			border-radius: 50rpx;
			margin: 0 auto;
			display: grid;
			align-items: center;
		}

	}
}

.inputl {
	text-align: left;
	font-family: PingFang SC, PingFang SC;
	font-size: 30rpx;
	font-weight: 600;
	color: #343434;
	line-height: 32rpx;
	width: 500rpx;
}

.input {
	text-align: right;
	font-family: PingFang SC, PingFang SC;
	font-size: 28rpx;
	color: #343434;
	line-height: 32rpx;
	width: 450rpx;
}

.shenfen ::v-deep .u-transition.data-v-39e33bf2.vue-ref.u-fade-enter-to.u-fade-enter-active:not(:first-child) {
	margin-top: 20rpx;
}

.popup {
	// width: 690rpx;
	height: 950rpx;
	margin-top: 40rpx;


}



.popup-footer {
	display: flex;
	justify-content: center;
	align-items: center;
	// margin: 30rpx 0;
	height: 146rpx;

	.zhixiao {
		height: 80rpx;
		background: #E8E8E8;
		//border-radius: 401rpx 401rpx 401rpx 401rpx;
		font-family: YouSheBiaoTiHei;
		font-weight: 400;
		font-size: 36rpx;
		color: #9C9C9C;
		line-height: 32rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		position: absolute;
		width: 100%;
		bottom: 0;
		border-radius: 0rpx 0rpx 20rpx 20rpx;
	}

	.shows_zhidao {
		background-color: #323232;
		color: #BBFC5B;
		font-weight: 400;
		font-size: 36rpx;
	}
}




// 滚动条样式
// ::v-deep ::-webkit-scrollbar {
// 	/*滚动条整体样式*/
// 	width: 4px !important;
// 	height: 1px !important;
// 	overflow: auto !important;
// 	background: #ccc !important;
// 	-webkit-appearance: auto !important;
// 	display: block;
// }

// ::v-deep ::-webkit-scrollbar-thumb {
// 	/*滚动条里面小方块*/
// 	border-radius: 10px !important;
// 	box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2) !important;
// 	background: #7b7979 !important;
// }

// ::v-deep ::-webkit-scrollbar-track {
// 	/*滚动条里面轨道*/
// 	// box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2) !important;
// 	// border-radius: 10px !important;
// 	background: #FFFFFF !important;
// }

.textarea_mph {
	::v-deep .u-textarea {
		height: 100%;
		padding: 20rpx;
		border: none;
		font-size: 26rpx;
		color: #9C9C9C;
		background: #F7F7F7;
		border-radius: 18rpx;
	}

	::v-deep .bttextarea {
		font-size: 24rpx;
		color: #9C9C9C;
	}
}

.popup_bq {
	display: flex;
	flex-direction: column;
	align-items: center;
	height: 950rpx;
	position: relative;

	img {
		position: absolute;
		width: 750rpx;
		height: 1040rpx;
		top: -164rpx;
		z-index: 100;
	}

	.bqlist {
		margin-top: 147rpx;
		z-index: 200;
		margin-left: 80rpx;
		height: 100%;

		.allmybqs {
			flex-wrap: wrap;
			width: 720rpx;
		}

		.allbqs {
			overflow-y: auto;
			-webkit-overflow-scrolling: touch;
			flex-wrap: wrap;
			height: 690rpx;
			width: 700rpx;

			.titles_fl {
				width: 112rpx;
				height: 39rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 600;
				font-size: 28rpx;
				color: #999999;
				line-height: 39rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}
		}

		.bqpiece {
			width: 210rpx;
			height: 70rpx;
			background: #F7F7F7;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			display: flex;
			justify-content: space-around;
			align-items: center;
			margin: 20rpx 20rpx 20rpx 0;

			span {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 26rpx;
				color: #323232;
				display: flex;
				align-items: center;
			}
		}

		.bqpiece.active {
			background: #C9FC7E;
		}
	}


}

.popup_tkall {
	// height: 1100rpx;
	z-index: 100;

	.popup_tk {
		font-size: 36rpx;
		font-weight: 800;
		margin: 12rpx 0 24rpx 0;
		text-align: center;
	}

	.popup-content {
		height: 900rpx;
		overflow-y: auto;
		margin-top: 30rpx;
	}

	.popup-content-item {
		width: 690rpx;
		padding: 30rpx;
		box-sizing: border-box;
		background: #F7F7F7;
		border-radius: 18rpx;
		border: 5rpx solid #EAEAEA;
		margin-bottom: 30rpx;
		font-family: PingFang SC, PingFang SC;
		justify-content: space-between;
		color: #3D3D3D;

		&.active {
			background: #FAFFF3;
			border: 5rpx solid;
			border-image: linear-gradient(270deg, rgba(251, 246, 109, 1), rgba(156, 234, 162, 1)) 2 2;
			border-radius: 18rpx;
			clip-path: inset(0px round 16rpx);
		}

		.popup-content-item-title {
			font-weight: bold;
			font-size: 36rpx;
		}

		.popup-content-item-content {
			font-weight: 400;
			font-size: 26rpx;
			line-height: 45rpx;

			::v-deep rich-text {
				margin-top: 20rpx;
				display: flex;
			}
		}
	}

	.closetk {
		color: #9C9C9C;
		font-size: 28rpx;
		font-weight: 400;
		font-family: PingFang SC, PingFang SC;
	}

	.confirmtk {
		color: #3D3D3D;
		font-size: 28rpx;
		font-weight: 400;
		font-family: PingFang SC, PingFang SC;
	}
}

.popup_tkallDraft {
	z-index: 100;
	background-color: #f7f7f7;

	.popup_tkDraft {
		font-size: 36rpx;
		margin: 12rpx 0 24rpx 0;
		text-align: center;
	}

	.popup-content {
		height: 900rpx;
		overflow-y: auto;
		margin-top: 30rpx;
		width: 100%;
		padding: 0rpx;
		background-color: #f7f7f7;

		.invoiceList {
			width: 690rpx;
			height: auto;

			.invoiceList-item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				background-color: #ffffff;
				width: 93%;
				margin-top: 20rpx;
				padding: 20rpx 20rpx;
				height: 220rpx;
				border-radius: 40rpx 40rpx 40rpx 40rpx;

				// &.active {
				// 	background-color: ##F0FFDF;
				// 	border: 2rpx solid #9CEAA2;
				// 	border-image: linear-gradient(270deg, rgba(251.00000023841858, 246.0000005364418, 109.00000110268593, 1), rgba(156.00000590085983, 234.00000125169754, 162.00000554323196, 1)) 2 2;
				// 	border-radius: 40rpx;
				// 	// clip-path: inset(0px round 16rpx);
				// }


				.item-img {
					width: 170rpx;
					height: 170rpx;
					// margin-left: 40rpx;

				}

				.item-con {
					margin-left: 20rpx;
					width: 90%;
					height: 160rpx;
					position: relative;
					color: #323232;

					.itenCon-actName {
						position: absolute;
						top: 0;
						font-size: 28rpx;
						font-weight: 400;
					}

					.itenCon-actCon {
						// position: absolute;
						// top: 100rpx;
						margin-top: 60rpx;
						font-size: 28rpx;
						font-weight: 400;
					}

					.itenCon-actPrice {
						position: absolute;
						bottom: 0;
						font-size: 26rpx;
						font-weight: 900;
					}
				}


			}

			.invoiceList-itemSelect {
				display: flex;
				justify-content: space-between;
				align-items: center;
				background-color: #F0FFDF;
				width: 93%;
				margin-top: 20rpx;
				padding: 20rpx 20rpx;
				height: 220rpx;
				border-radius: 40rpx 40rpx 40rpx 40rpx;
				border: 2rpx solid #9CEAA2;
				// border-image: linear-gradient(270deg, rgba(251.00000023841858, 246.0000005364418, 109.00000110268593, 1), rgba(156.00000590085983, 234.00000125169754, 162.00000554323196, 1)) 2 2;

				.item-img {
					width: 170rpx;
					height: 170rpx;
					// margin-left: 40rpx;

				}

				.item-con {
					margin-left: 20rpx;
					width: 70%;
					height: 160rpx;
					position: relative;
					color: #323232;
					display: grid;

					.itenCon-actName {
						// position: absolute;
						// top: 0;
						font-size: 28rpx;
						font-weight: 400;
					}

					.itenCon-actCon {
						// position: absolute;
						// top: 100rpx;
						// margin-top: 60rpx;
						font-size: 28rpx;
						font-weight: 400;
					}

					.itenCon-actPrice {
						// position: absolute;
						// bottom: 0;
						font-size: 26rpx;
						font-weight: 900;
					}
				}


			}
		}


		.popup-footer {
			width: 100%;
			display: flex;
			justify-content: center;
			align-items: center;

			.headBtn {
				width: 90%;
				height: 90rpx;
				background-color: #323232;
				border-radius: 148rpx;
				color: #BBFC5B;
				font-size: 36rpx;
				font-weight: 400;
				line-height: 50rpx;
				font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
				text-transform: none;
				font-style: normal;
				display: flex;
				justify-content: center;
				align-items: center;
				text-align: center;
				position: fixed;
				bottom: 66rpx;
				margin-left: 2%;
			}
		}


	}


}


.btn_1 {
	width: 90%;
	height: 80rpx;
	background: #323232;
	border-radius: 198rpx 198rpx 198rpx 198rpx;
	font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
	font-weight: 400;
	font-size: 32rpx;
	color: #BBFC5B;
	line-height: 80rpx;
	text-align: center;
	margin-top: 40rpx;
	z-index: 100;
}

.btn_2 {
	width: 50%;
	height: 80rpx;
	background: #323232;
	border-radius: 198rpx 198rpx 198rpx 198rpx;
	font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
	font-weight: 400;
	font-size: 32rpx;
	color: #BBFC5B;
	line-height: 80rpx;
	text-align: center;
	margin-top: 40rpx;
	z-index: 100;
}

.btn_3 {
	width: 50%;
	height: 80rpx;
	background: #E2E2E2;
	border-radius: 198rpx 198rpx 198rpx 198rpx;
	font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
	font-weight: 400;
	font-size: 32rpx;
	color: #999999;
	line-height: 80rpx;
	text-align: center;
	margin-top: 40rpx;
	z-index: 100;
}

.btn_4 {
	width: 50%;
	height: 80rpx;
	background: #ffffff;
	border: 1px solid #999999;
	border-radius: 198rpx 198rpx 198rpx 198rpx;
	font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
	font-weight: 400;
	font-size: 32rpx;
	color: #999999;
	line-height: 80rpx;
	text-align: center;
	margin-top: 40rpx;
	z-index: 100;
}
</style>