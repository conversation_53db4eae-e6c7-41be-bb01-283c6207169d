<template>
    <view class="box">
        <u-navbar :is-back="true" leftIconColor="#000000" :autoBack="true" bgColor="#ffffff" title="活动影集"
            :titleStyle="{ color: '#000000', fontSize: '34rpx', fontWeight: 'bold' }"></u-navbar>
        <view style="    padding-top: 88px;">
            <!--活动影集-->
            <view class="image-grid-container">
                <!-- 图片网格 -->
                <view class="image-grid">
                    <view v-for="(item, index) in list" :key="index" class="image-item" @click="previewImage(index)">
                        <image :src="item.image" mode="aspectFill" class="grid-image" @error="handleImageError" />
                    </view>
                </view>
            </view>
            <view class="flex flex-column flex-start align-items" v-if="list.length == 0" style="margin-top: 200rpx;">
                <image src="/static/message/activen.png" mode="" style="width: 180rpx;height: 180rpx;">
                </image>
                <view style="margin-top: 30rpx;font-size: 28rpx;color: #323232;">暂无影集</view>
            </view>
        </view>
    </view>
</template>
<script>
export default {
    data() {
        return {
            id: 0,
            list: []
        }

    },
    onLoad(e) {
        this.id = e.id;
        this.getImgList();
    },
    methods: {
        getImgList() {
            uni.$u.http.get(`/api/school.new_activity/image_list?id=${this.id}&page=1&limit=1000`).then(res => {
                this.list = res.data.list;
            }).catch(error => {
                console.error('获取图片列表失败:', error);
                uni.showToast({
                    title: '请求失败，请稍后再试',
                    icon: 'none',
                    duration: 2000
                });
            });
        },
        // 图片预览功能
        previewImage(index) {
            if (!this.list || this.list.length === 0) {
                uni.showToast({
                    title: '暂无图片可预览',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }

            const urls = this.list.map(item => item.image).filter(url => url);
            if (urls.length === 0) {
                uni.showToast({
                    title: '图片链接无效',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }

            uni.previewImage({
                current: index,
                urls: urls
            });
        },
        // 图片加载错误处理
        handleImageError(e) {
            console.error('图片加载失败:', e);
            uni.showToast({
                title: '图片加载失败',
                icon: 'none',
                duration: 1500
            });
        }
    }
}
</script>

<style lang="scss" scoped>
.box {
    background-color: #ffffff;
    min-height: 100vh;
}

.image-grid-container {
    padding: 30rpx 25rpx;
}

.empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400rpx;
    color: #999;
    font-size: 28rpx;
}

.image-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
}

.image-item {
    width: 168rpx;
    height: 168rpx;
    margin-right: 10rpx;
    margin-bottom: 10rpx;
    overflow: hidden;

    /* 每行第4个图片不需要右边距 */
    &:nth-child(4n) {
        margin-right: 0;
    }
}

.grid-image {
    width: 100%;
    height: 100%;
}

.flex {
	display: flex;
}

.justify-center {
	justify-content: center;
}

.space-between {
	justify-content: space-between;
}

.align-items {
	align-items: center;
}

.flex-column {
	flex-flow: column;
}

.justify-start {
	justify-content: start;
}

.mar-top-30 {
	margin-top: 30rpx;
}

</style>